<template>
  <div class="technician-tasks">
    <div class="page-header">
      <h1>我的任务</h1>
      <p>管理分配给您的维修任务</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="任务状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            style="width: 150px"
            @change="fetchTasks"
          >
            <el-option label="已分配" value="assigned" />
            <el-option label="已接受" value="accepted" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>

        <el-form-item label="紧急程度">
          <el-select
            v-model="filters.urgencyLevel"
            placeholder="全部程度"
            clearable
            style="width: 120px"
            @change="fetchTasks"
          >
            <el-option label="低" value="low" />
            <el-option label="中" value="normal" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="故障类型">
          <el-select
            v-model="filters.faultTypeId"
            placeholder="全部类型"
            clearable
            style="width: 150px"
            @change="fetchTasks"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="提交时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchTasks"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchTasks">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="tasks"
        style="width: 100%"
        empty-text="暂无任务"
        @row-click="viewTaskDetail"
      >
        <el-table-column prop="orderId" label="工单号" width="100" />

        <el-table-column label="车辆信息" width="180">
          <template #default="{ row }">
            <div class="vehicle-info">
              <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
              <div class="vehicle-detail">
                {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

        <el-table-column label="故障描述" min-width="200">
          <template #default="{ row }">
            <el-tooltip
              :content="row.description"
              placement="top"
              :disabled="row.description.length <= 50"
            >
              <div class="description-text">
                {{ row.description.length > 50 ? row.description.substring(0, 50) + '...' : row.description }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getUrgencyType(row.urgencyLevel)" size="small">
              {{ getUrgencyText(row.urgencyLevel) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="提交时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.submitTime) }}
          </template>
        </el-table-column>

        <el-table-column label="预估工时" width="100">
          <template #default="{ row }">
            {{ row.faultType?.estimatedHours || '-' }}h
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="canAcceptTask(row)"
              type="success"
              size="small"
              @click.stop="acceptTask(row)"
            >
              接受任务
            </el-button>
            <el-button
              v-if="canRejectTask(row)"
              type="danger"
              size="small"
              @click.stop="rejectTask(row)"
            >
              拒绝任务
            </el-button>
            <el-tag
              v-if="hasRespondedToTask(row)"
              :type="getResponseType(row)"
              size="small"
              style="margin-right: 8px;"
            >
              {{ getResponseText(row) }}
            </el-tag>
            <el-button
              v-if="row.status === 'accepted'"
              type="primary"
              size="small"
              @click.stop="startTask(row)"
            >
              开始维修
            </el-button>
            <el-button
              v-if="row.status === 'in_progress'"
              type="warning"
              size="small"
              @click.stop="completeTask(row)"
            >
              完成维修
            </el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="viewTaskDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchTasks"
          @current-change="fetchTasks"
        />
      </div>
    </el-card>

    <!-- 完成任务对话框 -->
    <el-dialog
      v-model="completeDialog.visible"
      title="完成维修任务"
      width="600px"
      @close="resetCompleteForm"
    >
      <el-form
        ref="completeFormRef"
        :model="completeForm"
        :rules="completeRules"
        label-width="100px"
      >
        <el-form-item label="实际工时" prop="workingHours">
          <el-input-number
            v-model="completeForm.workingHours"
            :min="0.1"
            :precision="1"
            :step="0.5"
            placeholder="请输入实际工时"
            style="width: 100%"
          />
          <div class="form-tip">单位：小时</div>
        </el-form-item>

        <el-form-item label="维修说明" prop="workResult">
          <el-input
            v-model="completeForm.workResult"
            type="textarea"
            :rows="4"
            placeholder="请描述维修过程和结果"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="使用材料">
          <div class="materials-section">
            <div
              v-for="(material, index) in completeForm.materials"
              :key="index"
              class="material-item"
            >
              <el-select
                v-model="material.materialId"
                placeholder="选择材料"
                style="width: 200px"
                filterable
                @change="updateMaterialPrice(material)"
              >
                <el-option
                  v-for="mat in availableMaterials"
                  :key="mat.materialId"
                  :label="`${mat.materialName} (${mat.specification})`"
                  :value="mat.materialId"
                />
              </el-select>
              <el-input-number
                v-model="material.quantity"
                :min="1"
                placeholder="数量"
                style="width: 120px"
                @change="updateMaterialPrice(material)"
              />
              <el-input-number
                v-model="material.totalPrice"
                :min="0"
                :precision="2"
                placeholder="总价"
                style="width: 120px"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeMaterial(index)"
              >
                删除
              </el-button>
            </div>
            <el-button type="text" @click="addMaterial">
              <el-icon><Plus /></el-icon>
              添加材料
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="completeDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="completing"
          @click="submitComplete"
        >
          完成任务
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus
} from '@element-plus/icons-vue'
import { technicianAPI } from '@/api/technician'
import { faultTypeAPI } from '@/api/faultType'
import { materialAPI } from '@/api/material'
import { orderAPI } from '@/api/order'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const completing = ref(false)
const tasks = ref([])
const faultTypes = ref([])
const availableMaterials = ref([])

// 表单引用
const completeFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  status: '',
  urgencyLevel: '',
  faultTypeId: null,
  dateRange: null
})

// 完成任务对话框
const completeDialog = reactive({
  visible: false,
  orderId: null
})

// 完成任务表单
const completeForm = reactive({
  workingHours: null,
  workResult: '',
  materials: []
})

// 完成任务表单验证规则
const completeRules = {
  workingHours: [
    { required: true, message: '请输入实际工时', trigger: 'blur' },
    { type: 'number', min: 0.1, message: '工时必须大于0', trigger: 'blur' }
  ],
  workResult: [
    { required: true, message: '请输入维修说明', trigger: 'blur' },
    { min: 10, message: '维修说明至少10个字符', trigger: 'blur' }
  ]
}

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.status) params.status = filters.status
    if (filters.urgencyLevel) params.urgencyLevel = filters.urgencyLevel
    if (filters.faultTypeId) params.faultTypeId = filters.faultTypeId
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await technicianAPI.getCurrentTechnicianOrders(params)
    console.log('Technician Tasks API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('Tasks content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      tasks.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      tasks.value = response.data
      pagination.total = response.data.length
    } else {
      tasks.value = []
      pagination.total = 0
    }

    console.log('Final tasks array:', tasks.value)
    console.log('Final pagination total:', pagination.total)
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    const response = await faultTypeAPI.getList()
    faultTypes.value = response.data.data?.content || response.data || []
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
  }
}

// 获取材料列表
const fetchMaterials = async () => {
  try {
    const response = await materialAPI.getList({ page: 1, size: 1000 })
    availableMaterials.value = response.data.data?.content || []
  } catch (error) {
    console.error('Failed to fetch materials:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    urgencyLevel: '',
    faultTypeId: null,
    dateRange: null
  })
  fetchTasks()
}

// 查看任务详情
const viewTaskDetail = (task) => {
  router.push(`/technician/tasks/${task.orderId}`)
}

// 接受任务
const acceptTask = async (task) => {
  try {
    await orderAPI.accept(task.orderId)
    ElMessage.success('任务已接受')
    fetchTasks()
  } catch (error) {
    console.error('Failed to accept task:', error)
    ElMessage.error('接受任务失败')
  }
}

// 拒绝任务
const rejectTask = async (task) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因',
      '拒绝任务',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝原因'
      }
    )

    await orderAPI.reject(task.orderId, { reason })
    ElMessage.success('任务已拒绝')
    fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to reject task:', error)

      // 美化错误提示信息
      let errorMessage = '拒绝任务失败'
      if (error.response?.data?.message) {
        const message = error.response.data.message
        if (message.includes('没有可用的') && message.includes('技师进行重新分配')) {
          errorMessage = '拒绝失败：当前没有其他可用技师可以接替此任务，请联系管理员处理'
        } else {
          errorMessage = message
        }
      }

      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 5000,
        showClose: true
      })
    }
  }
}

// 开始任务
const startTask = async (task) => {
  try {
    await orderAPI.start(task.orderId)
    ElMessage.success('任务已开始')
    fetchTasks()
  } catch (error) {
    console.error('Failed to start task:', error)
    ElMessage.error('开始任务失败')
  }
}

// 完成任务
const completeTask = (task) => {
  completeDialog.orderId = task.orderId
  completeDialog.visible = true

  // 设置预估工时作为默认值
  completeForm.workingHours = task.faultType?.estimatedHours || null
}

// 添加材料
const addMaterial = () => {
  completeForm.materials.push({
    materialId: null,
    quantity: 1,
    totalPrice: 0
  })
}

// 删除材料
const removeMaterial = (index) => {
  completeForm.materials.splice(index, 1)
}

// 更新材料价格
const updateMaterialPrice = (material) => {
  if (material.materialId && material.quantity > 0) {
    const selectedMaterial = availableMaterials.value.find(m => m.materialId === material.materialId)
    if (selectedMaterial) {
      material.totalPrice = selectedMaterial.unitPrice * material.quantity
    }
  }
}

// 提交完成任务
const submitComplete = async () => {
  if (!completeFormRef.value) return

  try {
    await completeFormRef.value.validate()
    completing.value = true

    // 过滤掉未选择材料的项，并确保包含必要字段
    const validMaterials = completeForm.materials
      .filter(m => m.materialId && m.quantity > 0)
      .map(m => ({
        materialId: m.materialId,
        quantity: m.quantity,
        totalPrice: m.totalPrice || 0
      }))

    const completeData = {
      workingHours: completeForm.workingHours,
      workResult: completeForm.workResult,
      materialsUsed: validMaterials
    }

    await orderAPI.complete(completeDialog.orderId, completeData)

    ElMessage.success('任务已完成')
    completeDialog.visible = false
    fetchTasks()
  } catch (error) {
    console.error('Failed to complete task:', error)
  } finally {
    completing.value = false
  }
}

// 重置完成任务表单
const resetCompleteForm = () => {
  Object.assign(completeForm, {
    workingHours: null,
    workResult: '',
    materials: []
  })
  if (completeFormRef.value) {
    completeFormRef.value.resetFields()
  }
}

// 获取紧急程度类型
const getUrgencyType = (level) => {
  const typeMap = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (level) => {
  const textMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[level] || level
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    assigned: 'info',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

// 判断是否可以接受任务
const canAcceptTask = (task) => {
  if (task.status !== 'assigned') return false

  // 检查当前技师的分配状态
  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus === 'pending'
}

// 判断是否可以拒绝任务
const canRejectTask = (task) => {
  if (task.status !== 'assigned') return false

  // 检查当前技师的分配状态
  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus === 'pending'
}

// 判断当前技师是否已经响应任务
const hasRespondedToTask = (task) => {
  if (task.status !== 'assigned') return false

  const currentTechnicianAssignment = getCurrentTechnicianAssignment(task)
  if (!currentTechnicianAssignment) return false

  return currentTechnicianAssignment.agreementStatus !== 'pending'
}

// 获取当前技师的分配记录
const getCurrentTechnicianAssignment = (task) => {
  if (!task.technicianAssignments || !Array.isArray(task.technicianAssignments)) {
    return null
  }

  // 这里需要获取当前登录技师的ID，暂时使用一个假设的方法
  const currentTechnicianId = getCurrentTechnicianId()

  return task.technicianAssignments.find(assignment =>
    assignment.technicianId === currentTechnicianId
  )
}

// 获取响应状态的类型
const getResponseType = (task) => {
  const assignment = getCurrentTechnicianAssignment(task)
  if (!assignment) return 'info'

  const typeMap = {
    accepted: 'success',
    rejected: 'danger',
    pending: 'warning'
  }
  return typeMap[assignment.agreementStatus] || 'info'
}

// 获取响应状态的文本
const getResponseText = (task) => {
  const assignment = getCurrentTechnicianAssignment(task)
  if (!assignment) return ''

  const textMap = {
    accepted: '已同意',
    rejected: '已拒绝',
    pending: '待确认'
  }
  return textMap[assignment.agreementStatus] || ''
}

// 获取当前技师ID
const getCurrentTechnicianId = () => {
  const authStore = useAuthStore()
  return authStore.user?.userId || null
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks()
  fetchFaultTypes()
  fetchMaterials()
})
</script>

<style scoped>
.technician-tasks {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.license-plate {
  font-weight: 600;
  color: #409eff;
}

.vehicle-detail {
  font-size: 12px;
  color: #999;
}

.description-text {
  line-height: 1.4;
  word-break: break-word;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 完成任务对话框样式 */
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.materials-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.material-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 20px;
  }

  .filter-form {
    :deep(.el-form-item) {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }

  .material-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .filter-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
    }

    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }
}
</style>
