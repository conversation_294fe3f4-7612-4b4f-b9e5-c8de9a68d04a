import api from './index'

export const technicianAPI = {
  // 技师注册
  register(data) {
    return api.post('/technicians', data)
  },

  // 获取工种列表
  getSpecialties() {
    return api.get('/technicians/specialties')
  },

  // 获取当前技师信息
  getCurrentTechnician() {
    return api.get('/technicians/me')
  },

  // 更新当前技师信息
  updateCurrentTechnician(data) {
    return api.put('/technicians/me', data)
  },

  // 获取当前技师工单列表
  getCurrentTechnicianOrders(params = {}) {
    return api.get('/technicians/me/orders', { params })
  },

  // 获取当前技师工时费收入
  getCurrentTechnicianPayments(params = {}) {
    return api.get('/technicians/me/payments', { params })
  },

  // 获取当前技师历史记录
  getCurrentTechnicianHistory(params = {}) {
    return api.get('/technicians/me/history', { params })
  },

  // 获取当前技师统计数据
  getCurrentTechnicianStats() {
    return api.get('/technicians/me/stats')
  }

}
