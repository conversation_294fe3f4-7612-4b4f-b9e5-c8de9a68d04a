package com.example.service;

import com.example.entity.*;
import com.example.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class TestDataHelper {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private OrderMaterialUsageRepository orderMaterialUsageRepository;

    @Autowired
    private OrderFeedbackRepository orderFeedbackRepository;

    @Autowired
    private OrderStatusHistoryRepository orderStatusHistoryRepository;

    @Autowired
    private OrderTechnicianAssignmentRepository orderTechnicianAssignmentRepository;

    @Autowired
    private LaborPaymentRepository laborPaymentRepository;

    @Autowired
    private OrderUrgentRequestRepository orderUrgentRequestRepository;

    /**
     * 创建进行中的工单
     */
    public void createInProgressOrders(List<User> users, List<Vehicle> vehicles, List<Technician> technicians,
                                     List<FaultType> faultTypes, Random random, int count) {
        for (int i = 0; i < count; i++) {
            User user = users.get(random.nextInt(users.size()));
            Vehicle vehicle = vehicles.stream()
                    .filter(v -> v.getUser().getUserId().equals(user.getUserId()))
                    .findFirst().orElse(vehicles.get(0));
            FaultType faultType = faultTypes.get(random.nextInt(faultTypes.size()));

            RepairOrder order = new RepairOrder();
            order.setDescription(generateOrderDescription(faultType));
            order.setUrgencyLevel(RepairOrder.UrgencyLevel.values()[random.nextInt(4)]);
            order.setUser(user);
            order.setVehicle(vehicle);
            order.setFaultType(faultType);
            order.setContactPhone(user.getPhone());
            order.setStatus(RepairOrder.OrderStatus.IN_PROGRESS);
            order.setPaymentStatus(RepairOrder.PaymentStatus.UNPAID);

            // 设置时间
            LocalDateTime submitTime = LocalDateTime.now().minusDays(random.nextInt(7) + 1);
            order.setSubmitTime(submitTime);

            // 设置期望维修时间：在提交时间后1-3天内的工作时间（8:00-18:00）
            LocalDateTime preferredTime = submitTime.plusDays(random.nextInt(3) + 1);
            // 确保在工作时间内
            preferredTime = preferredTime.withHour(8 + random.nextInt(10)); // 8:00-17:00
            preferredTime = preferredTime.withMinute(random.nextInt(60));
            order.setPreferredTime(preferredTime);

            // 预估完成时间 = 期望维修时间 + 预计工时
            order.setEstimatedCompletionTime(preferredTime.plusHours(faultType.getEstimatedHours()));

            RepairOrder savedOrder = repairOrderRepository.save(order);

            // 分配技师
            assignTechniciansToOrder(savedOrder, technicians, faultType);

            // 添加状态历史
            addOrderStatusHistory(savedOrder);

            // 随机添加催单记录（30%的概率）
            if (random.nextDouble() < 0.3) {
                addUrgentRequests(savedOrder, random);
            }
        }
    }

    // 删除了createPendingOrders方法，根据用户要求不允许测试数据创建待处理订单

    /**
     * 生成工单描述
     */
    public String generateOrderDescription(FaultType faultType) {
        Map<String, String[]> descriptions = new HashMap<>();
        descriptions.put("发动机故障", new String[]{
            "发动机启动困难，需要多次打火才能启动",
            "发动机怠速不稳，有明显抖动现象",
            "发动机异响，疑似内部零件磨损",
            "发动机动力不足，加速无力",
            "发动机冒黑烟，燃烧不充分"
        });
        descriptions.put("变速箱故障", new String[]{
            "变速箱换挡困难，挂挡有顿挫感",
            "变速箱异响，行驶中有异常声音",
            "变速箱漏油，地面有油渍",
            "自动变速箱不升档，一直在低档位",
            "变速箱打滑，加油门转速上升但车速不增加"
        });
        descriptions.put("制动系统故障", new String[]{
            "刹车失灵，制动距离明显增长",
            "刹车异响，踩刹车时有尖锐声音",
            "刹车踏板发软，踩下去没有力度",
            "刹车盘磨损严重，需要更换",
            "刹车片厚度不足，制动效果差"
        });
        descriptions.put("电气系统故障", new String[]{
            "车灯不亮，疑似电路故障",
            "电瓶亏电，无法正常启动",
            "发电机故障，充电系统异常",
            "保险丝烧断，部分电器无法使用",
            "线路老化，存在安全隐患"
        });
        descriptions.put("空调系统故障", new String[]{
            "空调不制冷，出风口温度过高",
            "空调异响，压缩机工作异常",
            "空调漏氟，制冷效果差",
            "空调滤芯堵塞，出风量小",
            "空调控制面板故障，无法调节温度"
        });
        descriptions.put("底盘故障", new String[]{
            "悬挂异响，过减速带时有异常声音",
            "转向困难，方向盘沉重",
            "减震器漏油，减震效果差",
            "底盘异响，行驶中有金属摩擦声",
            "四轮定位不准，车辆跑偏"
        });
        descriptions.put("车身损坏", new String[]{
            "车身划痕，需要钣金修复",
            "车门变形，无法正常开关",
            "保险杠破损，需要更换",
            "车身锈蚀，需要防锈处理",
            "内饰破损，需要修复或更换"
        });
        descriptions.put("轮胎故障", new String[]{
            "轮胎磨损严重，需要更换",
            "轮胎扎钉，需要补胎",
            "轮胎气压不足，需要充气",
            "轮胎老化开裂，存在安全隐患",
            "备胎损坏，需要更换"
        });

        String[] typeDescriptions = descriptions.get(faultType.getTypeName());
        if (typeDescriptions != null) {
            Random random = new Random();
            return typeDescriptions[random.nextInt(typeDescriptions.length)];
        }
        return "车辆故障，需要检修";
    }

    /**
     * 生成工作结果
     */
    public String generateWorkResult(FaultType faultType) {
        Map<String, String[]> results = new HashMap<>();
        results.put("发动机故障", new String[]{
            "更换火花塞，清洗节气门，故障已排除",
            "更换机油和机滤，发动机运行正常",
            "修复发动机线路，启动正常",
            "更换发动机支架，异响消除",
            "清洗喷油嘴，动力恢复正常"
        });
        results.put("变速箱故障", new String[]{
            "更换变速箱油，换挡顺畅",
            "修复变速箱密封件，漏油问题解决",
            "调整变速箱控制模块，升档正常",
            "更换变速箱滤网，异响消除",
            "修复变速箱离合器，打滑问题解决"
        });
        results.put("制动系统故障", new String[]{
            "更换刹车片和刹车盘，制动效果恢复",
            "更换制动液，刹车踏板力度正常",
            "修复制动管路，刹车失灵问题解决",
            "调整刹车间隙，异响消除",
            "更换刹车总泵，制动系统正常"
        });

        String[] typeResults = results.get(faultType.getTypeName());
        if (typeResults != null) {
            Random random = new Random();
            return typeResults[random.nextInt(typeResults.length)];
        }
        return "故障已修复，车辆运行正常";
    }

    /**
     * 为工单分配技师
     */
    public void assignTechniciansToOrder(RepairOrder order, List<Technician> technicians, FaultType faultType) {
        Set<Technician.Specialty> requiredSpecialties = faultType.getRequiredSpecialties();
        int requiredTechCount = faultType.getRequiredTechCount();
        List<Technician> assignedTechnicians = new ArrayList<>();

        // 如果只需要一种工种，按需要的技师数量分配
        if (requiredSpecialties.size() == 1) {
            Technician.Specialty specialty = requiredSpecialties.iterator().next();
            List<Technician> availableTechnicians = technicians.stream()
                    .filter(t -> t.getSpecialty() == specialty && t.getStatus() == 1)
                    .sorted(Comparator.comparing(Technician::getWorkload))
                    .collect(Collectors.toList());

            // 分配所需数量的技师（但不超过可用技师数量）
            int techsToAssign = Math.min(requiredTechCount, availableTechnicians.size());
            for (int i = 0; i < techsToAssign; i++) {
                Technician selectedTechnician = availableTechnicians.get(i);
                assignedTechnicians.add(selectedTechnician);
                selectedTechnician.setWorkload(selectedTechnician.getWorkload() + 1);
            }
        } else {
            // 如果需要多种工种，每种工种分配一个技师
            for (Technician.Specialty specialty : requiredSpecialties) {
                List<Technician> availableTechnicians = technicians.stream()
                        .filter(t -> t.getSpecialty() == specialty && t.getStatus() == 1)
                        .sorted(Comparator.comparing(Technician::getWorkload))
                        .collect(Collectors.toList());

                if (!availableTechnicians.isEmpty()) {
                    Technician selectedTechnician = availableTechnicians.get(0);
                    assignedTechnicians.add(selectedTechnician);
                    selectedTechnician.setWorkload(selectedTechnician.getWorkload() + 1);
                }
            }
        }

        // 创建技师分配记录
        List<OrderTechnicianAssignment> assignments = new ArrayList<>();
        for (Technician technician : assignedTechnicians) {
            OrderTechnicianAssignment assignment = new OrderTechnicianAssignment(order, technician);

            // 对于测试数据，直接设置为已同意状态
            assignment.setAgreementStatus(OrderTechnicianAssignment.AgreementStatus.ACCEPTED);

            // 设置响应时间：在工单提交后1-3小时内
            LocalDateTime responseTime = order.getSubmitTime().plusMinutes(60 + new Random().nextInt(120));
            assignment.setResponseTime(responseTime);

            assignments.add(assignment);
            orderTechnicianAssignmentRepository.save(assignment);
        }

        order.setTechnicianAssignments(assignments);
        repairOrderRepository.save(order);
    }

    /**
     * 添加材料使用记录
     */
    public void addMaterialUsage(RepairOrder order, List<Material> materials, Random random) {
        int materialCount = random.nextInt(3) + 1; // 1-3种材料
        Set<Material> usedMaterials = new HashSet<>();

        for (int i = 0; i < materialCount; i++) {
            Material material = materials.get(random.nextInt(materials.size()));
            if (!usedMaterials.contains(material)) {
                usedMaterials.add(material);

                OrderMaterialUsage usage = new OrderMaterialUsage();
                usage.setRepairOrder(order);
                usage.setMaterial(material);

                // 修复：使用整数数量，范围1-10个
                int intQuantity = random.nextInt(10) + 1; // 1-10个单位
                BigDecimal quantity = new BigDecimal(intQuantity);
                usage.setQuantity(quantity);
                usage.setTotalPrice(quantity.multiply(material.getUnitPrice()));

                // 修复：材料使用时间应该在工单开始维修后到完成前的时间范围内
                LocalDateTime startTime = order.getSubmitTime().plusHours(2); // 开始维修时间
                LocalDateTime endTime = order.getActualCompletionTime() != null ?
                    order.getActualCompletionTime() : order.getEstimatedCompletionTime();

                // 在开始维修到完成之间的随机时间
                long hoursBetween = java.time.Duration.between(startTime, endTime).toHours();
                if (hoursBetween > 0) {
                    usage.setUseTime(startTime.plusHours(random.nextInt((int)hoursBetween + 1)));
                } else {
                    usage.setUseTime(startTime);
                }

                orderMaterialUsageRepository.save(usage);
            }
        }
    }

    /**
     * 计算工单费用
     */
    public void calculateOrderCosts(RepairOrder order, List<Technician> technicians) {
        // 计算工时费 - 通过技师分配记录查询
        BigDecimal laborCost = BigDecimal.ZERO;
        if (order.getWorkingHours() != null) {
            // 从数据库查询技师分配记录，预加载技师信息避免懒加载问题
            List<OrderTechnicianAssignment> assignments = orderTechnicianAssignmentRepository
                    .findByRepairOrderOrderIdWithTechnician(order.getOrderId());

            for (OrderTechnicianAssignment assignment : assignments) {
                Technician technician = assignment.getTechnician();
                laborCost = laborCost.add(order.getWorkingHours().multiply(technician.getHourlyRate()));
            }
        }

        // 计算材料费 - 从数据库重新查询材料使用记录
        BigDecimal materialCost = orderMaterialUsageRepository.calculateTotalMaterialCostByOrderId(order.getOrderId());

        order.setTotalLaborCost(laborCost);
        order.setTotalMaterialCost(materialCost);
        order.setTotalCost(laborCost.add(materialCost));

        repairOrderRepository.save(order);
    }

    /**
     * 添加工单反馈
     */
    public void addOrderFeedback(RepairOrder order, Random random) {
        OrderFeedback feedback = new OrderFeedback();
        feedback.setRepairOrder(order);
        feedback.setRating(random.nextInt(2) + 4); // 4-5分

        String[] comments = {
            "服务很好，技师专业，修复及时",
            "价格合理，质量可靠，推荐",
            "技师态度好，工作认真负责",
            "修复效果很好，车辆运行正常",
            "服务周到，解释详细，满意"
        };
        feedback.setComment(comments[random.nextInt(comments.length)]);

        // 修复：反馈时间应该在工单完成后的合理时间内
        LocalDateTime completionTime = order.getActualCompletionTime() != null ?
            order.getActualCompletionTime() : order.getEstimatedCompletionTime();

        // 反馈时间在完成后1小时到7天内
        LocalDateTime feedbackTime = completionTime.plusHours(1 + random.nextInt(167)); // 1小时到7天
        feedback.setFeedbackTime(feedbackTime);

        orderFeedbackRepository.save(feedback);
    }

    /**
     * 添加工单状态历史
     */
    public void addOrderStatusHistory(RepairOrder order) {
        // 添加初始状态
        OrderStatusHistory history = new OrderStatusHistory();
        history.setRepairOrder(order);
        history.setFromStatus(null);
        history.setToStatus(RepairOrder.OrderStatus.PENDING);
        history.setChangeTime(order.getSubmitTime());
        history.setOperator("系统");
        history.setRemark("工单创建");

        orderStatusHistoryRepository.save(history);

        // 如果是已完成或进行中的工单，添加更多状态历史
        if (order.getStatus() != RepairOrder.OrderStatus.PENDING) {
            addStatusTransition(order, RepairOrder.OrderStatus.PENDING, RepairOrder.OrderStatus.ASSIGNED,
                              order.getSubmitTime().plusMinutes(30), "系统", "自动分配技师");

            addStatusTransition(order, RepairOrder.OrderStatus.ASSIGNED, RepairOrder.OrderStatus.ACCEPTED,
                              order.getSubmitTime().plusHours(1), "技师", "技师接受任务");

            addStatusTransition(order, RepairOrder.OrderStatus.ACCEPTED, RepairOrder.OrderStatus.IN_PROGRESS,
                              order.getSubmitTime().plusHours(2), "技师", "开始维修工作");

            if (order.getStatus() == RepairOrder.OrderStatus.COMPLETED) {
                addStatusTransition(order, RepairOrder.OrderStatus.IN_PROGRESS, RepairOrder.OrderStatus.COMPLETED,
                                  order.getActualCompletionTime(), "技师", "维修工作完成");
            }
        }
    }

    private void addStatusTransition(RepairOrder order, RepairOrder.OrderStatus fromStatus,
                                   RepairOrder.OrderStatus toStatus, LocalDateTime changeTime,
                                   String operator, String remark) {
        OrderStatusHistory history = new OrderStatusHistory();
        history.setRepairOrder(order);
        history.setFromStatus(fromStatus);
        history.setToStatus(toStatus);
        history.setChangeTime(changeTime);
        history.setOperator(operator);
        history.setRemark(remark);

        orderStatusHistoryRepository.save(history);
    }

    /**
     * 为已完成的订单创建工时费记录
     */
    public void createLaborPayments(RepairOrder order) {
        if (order.getStatus() != RepairOrder.OrderStatus.COMPLETED || order.getActualCompletionTime() == null) {
            return;
        }

        LocalDateTime completionTime = order.getActualCompletionTime();
        int year = completionTime.getYear();
        int month = completionTime.getMonthValue();
        BigDecimal workingHours = order.getWorkingHours();

        // 为每个分配的技师创建或更新工时费记录
        for (Technician technician : order.getAssignedTechnicians()) {
            // 查找该技师当月的工时费记录
            LaborPayment existingPayment = laborPaymentRepository
                    .findByTechnicianTechnicianIdAndYearAndMonth(technician.getTechnicianId(), year, month)
                    .orElse(null);

            if (existingPayment != null) {
                // 更新现有记录
                existingPayment.setTotalHours(existingPayment.getTotalHours().add(workingHours));
                existingPayment.setTotalAmount(existingPayment.getTotalHours().multiply(existingPayment.getHourlyRate()));
                laborPaymentRepository.save(existingPayment);
            } else {
                // 创建新记录
                LaborPayment newPayment = new LaborPayment(year, month, workingHours, technician.getHourlyRate(), technician);
                laborPaymentRepository.save(newPayment);
            }
        }
    }

    /**
     * 添加催单记录
     */
    public void addUrgentRequests(RepairOrder order, Random random) {
        String[] urgentReasons = {
            "急需用车，请尽快完成维修",
            "明天有重要会议需要用车",
            "工期紧急，希望能加快进度",
            "车辆故障影响日常出行，请优先处理",
            "已经等待较长时间，希望尽快完成",
            "需要赶在周末前完成维修",
            "有紧急出差需要，请加急处理"
        };

        // 随机生成1-2个催单记录
        int urgentCount = random.nextInt(2) + 1;

        for (int i = 0; i < urgentCount; i++) {
            OrderUrgentRequest urgentRequest = new OrderUrgentRequest();
            urgentRequest.setRepairOrder(order);
            urgentRequest.setReason(urgentReasons[random.nextInt(urgentReasons.length)]);

            // 催单时间在工单提交后1-3天内
            LocalDateTime urgentTime = order.getSubmitTime().plusDays(random.nextInt(3) + 1)
                    .plusHours(random.nextInt(12));
            urgentRequest.setUrgentTime(urgentTime);

            // 随机设置催单状态（80%待处理，20%已处理）
            if (random.nextDouble() < 0.8) {
                urgentRequest.setStatus(OrderUrgentRequest.UrgentStatus.PENDING);
            } else {
                urgentRequest.setStatus(OrderUrgentRequest.UrgentStatus.PROCESSED);
            }

            orderUrgentRequestRepository.save(urgentRequest);
        }
    }
}
