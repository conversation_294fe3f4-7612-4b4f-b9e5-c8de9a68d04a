package com.example.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 维修工单DTO
 */
public class OrderDTO {

    private Long orderId;
    private Long userId;
    private Long vehicleId;
    private Long faultTypeId;
    private String description;
    private String urgencyLevel;
    private LocalDateTime submitTime;
    private LocalDateTime preferredTime;
    private LocalDateTime estimatedCompletionTime;
    private LocalDateTime actualCompletionTime;
    private String status;
    private String paymentStatus;
    private String contactPhone;
    private BigDecimal totalLaborCost;
    private BigDecimal totalMaterialCost;
    private BigDecimal totalCost;
    private String workResult;
    private BigDecimal workingHours;

    // 关联对象信息
    private UserInfo user;
    private VehicleInfo vehicle;
    private FaultTypeInfo faultType;
    private List<TechnicianInfo> assignedTechnicians;
    private List<TechnicianAssignmentInfo> technicianAssignments;
    private List<MaterialUsageInfo> materialUsages;
    private FeedbackInfo feedback;
    private List<UrgentRequestInfo> urgentRequests;

    public OrderDTO() {}

    // 内部类：用户信息
    public static class UserInfo {
        private Long userId;
        private String username;
        private String realName;
        private String phone;

        public UserInfo() {}

        public UserInfo(Long userId, String username, String realName, String phone) {
            this.userId = userId;
            this.username = username;
            this.realName = realName;
            this.phone = phone;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    // 内部类：车辆信息
    public static class VehicleInfo {
        private Long vehicleId;
        private String licensePlate;
        private String brand;
        private String model;

        public VehicleInfo() {}

        public VehicleInfo(Long vehicleId, String licensePlate, String brand, String model) {
            this.vehicleId = vehicleId;
            this.licensePlate = licensePlate;
            this.brand = brand;
            this.model = model;
        }

        public Long getVehicleId() {
            return vehicleId;
        }

        public void setVehicleId(Long vehicleId) {
            this.vehicleId = vehicleId;
        }

        public String getLicensePlate() {
            return licensePlate;
        }

        public void setLicensePlate(String licensePlate) {
            this.licensePlate = licensePlate;
        }

        public String getBrand() {
            return brand;
        }

        public void setBrand(String brand) {
            this.brand = brand;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }
    }

    // 内部类：故障类型信息
    public static class FaultTypeInfo {
        private Long faultTypeId;
        private String typeName;
        private List<String> requiredSpecialties;
        private Integer estimatedHours;

        public FaultTypeInfo() {}

        public FaultTypeInfo(Long faultTypeId, String typeName, List<String> requiredSpecialties, Integer estimatedHours) {
            this.faultTypeId = faultTypeId;
            this.typeName = typeName;
            this.requiredSpecialties = requiredSpecialties;
            this.estimatedHours = estimatedHours;
        }

        public Long getFaultTypeId() {
            return faultTypeId;
        }

        public void setFaultTypeId(Long faultTypeId) {
            this.faultTypeId = faultTypeId;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }

        public List<String> getRequiredSpecialties() {
            return requiredSpecialties;
        }

        public void setRequiredSpecialties(List<String> requiredSpecialties) {
            this.requiredSpecialties = requiredSpecialties;
        }

        public Integer getEstimatedHours() {
            return estimatedHours;
        }

        public void setEstimatedHours(Integer estimatedHours) {
            this.estimatedHours = estimatedHours;
        }
    }

    // 内部类：技师信息
    public static class TechnicianInfo {
        private Long technicianId;
        private String realName;
        private String specialty;
        private String phone;

        public TechnicianInfo() {}

        public TechnicianInfo(Long technicianId, String realName, String specialty, String phone) {
            this.technicianId = technicianId;
            this.realName = realName;
            this.specialty = specialty;
            this.phone = phone;
        }

        public Long getTechnicianId() {
            return technicianId;
        }

        public void setTechnicianId(Long technicianId) {
            this.technicianId = technicianId;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getSpecialty() {
            return specialty;
        }

        public void setSpecialty(String specialty) {
            this.specialty = specialty;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    // 内部类：技师分配信息（包含同意状态）
    public static class TechnicianAssignmentInfo {
        private Long technicianId;
        private String realName;
        private String specialty;
        private String phone;
        private String agreementStatus; // pending, accepted, rejected
        private LocalDateTime responseTime;

        public TechnicianAssignmentInfo() {}

        public TechnicianAssignmentInfo(Long technicianId, String realName, String specialty, String phone,
                                      String agreementStatus, LocalDateTime responseTime) {
            this.technicianId = technicianId;
            this.realName = realName;
            this.specialty = specialty;
            this.phone = phone;
            this.agreementStatus = agreementStatus;
            this.responseTime = responseTime;
        }

        public Long getTechnicianId() {
            return technicianId;
        }

        public void setTechnicianId(Long technicianId) {
            this.technicianId = technicianId;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getSpecialty() {
            return specialty;
        }

        public void setSpecialty(String specialty) {
            this.specialty = specialty;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getAgreementStatus() {
            return agreementStatus;
        }

        public void setAgreementStatus(String agreementStatus) {
            this.agreementStatus = agreementStatus;
        }



        public LocalDateTime getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(LocalDateTime responseTime) {
            this.responseTime = responseTime;
        }
    }

    // 主要属性的getter和setter方法
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Long getFaultTypeId() {
        return faultTypeId;
    }

    public void setFaultTypeId(Long faultTypeId) {
        this.faultTypeId = faultTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setUrgencyLevel(String urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }

    public LocalDateTime getPreferredTime() {
        return preferredTime;
    }

    public void setPreferredTime(LocalDateTime preferredTime) {
        this.preferredTime = preferredTime;
    }

    public LocalDateTime getEstimatedCompletionTime() {
        return estimatedCompletionTime;
    }

    public void setEstimatedCompletionTime(LocalDateTime estimatedCompletionTime) {
        this.estimatedCompletionTime = estimatedCompletionTime;
    }

    public LocalDateTime getActualCompletionTime() {
        return actualCompletionTime;
    }

    public void setActualCompletionTime(LocalDateTime actualCompletionTime) {
        this.actualCompletionTime = actualCompletionTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }

    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }

    public BigDecimal getTotalMaterialCost() {
        return totalMaterialCost;
    }

    public void setTotalMaterialCost(BigDecimal totalMaterialCost) {
        this.totalMaterialCost = totalMaterialCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public String getWorkResult() {
        return workResult;
    }

    public void setWorkResult(String workResult) {
        this.workResult = workResult;
    }

    public BigDecimal getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigDecimal workingHours) {
        this.workingHours = workingHours;
    }

    public UserInfo getUser() {
        return user;
    }

    public void setUser(UserInfo user) {
        this.user = user;
    }

    public VehicleInfo getVehicle() {
        return vehicle;
    }

    public void setVehicle(VehicleInfo vehicle) {
        this.vehicle = vehicle;
    }

    public FaultTypeInfo getFaultType() {
        return faultType;
    }

    public void setFaultType(FaultTypeInfo faultType) {
        this.faultType = faultType;
    }

    public List<TechnicianInfo> getAssignedTechnicians() {
        return assignedTechnicians;
    }

    public void setAssignedTechnicians(List<TechnicianInfo> assignedTechnicians) {
        this.assignedTechnicians = assignedTechnicians;
    }

    public List<TechnicianAssignmentInfo> getTechnicianAssignments() {
        return technicianAssignments;
    }

    public void setTechnicianAssignments(List<TechnicianAssignmentInfo> technicianAssignments) {
        this.technicianAssignments = technicianAssignments;
    }

    public List<MaterialUsageInfo> getMaterialUsages() {
        return materialUsages;
    }

    public void setMaterialUsages(List<MaterialUsageInfo> materialUsages) {
        this.materialUsages = materialUsages;
    }

    public FeedbackInfo getFeedback() {
        return feedback;
    }

    public void setFeedback(FeedbackInfo feedback) {
        this.feedback = feedback;
    }

    public List<UrgentRequestInfo> getUrgentRequests() {
        return urgentRequests;
    }

    public void setUrgentRequests(List<UrgentRequestInfo> urgentRequests) {
        this.urgentRequests = urgentRequests;
    }

    // 内部类：材料使用信息
    public static class MaterialUsageInfo {
        private Long usageId;
        private Long materialId;
        private String materialName;
        private String specification;
        private String unit;
        private BigDecimal unitPrice;
        private BigDecimal quantity;
        private BigDecimal totalPrice;
        private LocalDateTime useTime;

        public MaterialUsageInfo() {}

        public MaterialUsageInfo(Long usageId, Long materialId, String materialName, String specification,
                               String unit, BigDecimal unitPrice, BigDecimal quantity, BigDecimal totalPrice,
                               LocalDateTime useTime) {
            this.usageId = usageId;
            this.materialId = materialId;
            this.materialName = materialName;
            this.specification = specification;
            this.unit = unit;
            this.unitPrice = unitPrice;
            this.quantity = quantity;
            this.totalPrice = totalPrice;
            this.useTime = useTime;
        }

        public Long getUsageId() {
            return usageId;
        }

        public void setUsageId(Long usageId) {
            this.usageId = usageId;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Long materialId) {
            this.materialId = materialId;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getSpecification() {
            return specification;
        }

        public void setSpecification(String specification) {
            this.specification = specification;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public BigDecimal getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public BigDecimal getQuantity() {
            return quantity;
        }

        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }

        public BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }

        public LocalDateTime getUseTime() {
            return useTime;
        }

        public void setUseTime(LocalDateTime useTime) {
            this.useTime = useTime;
        }
    }

    // 内部类：反馈信息
    public static class FeedbackInfo {
        private Long feedbackId;
        private Integer rating;
        private String comment;
        private LocalDateTime feedbackTime;

        public FeedbackInfo() {}

        public FeedbackInfo(Long feedbackId, Integer rating, String comment, LocalDateTime feedbackTime) {
            this.feedbackId = feedbackId;
            this.rating = rating;
            this.comment = comment;
            this.feedbackTime = feedbackTime;
        }

        public Long getFeedbackId() {
            return feedbackId;
        }

        public void setFeedbackId(Long feedbackId) {
            this.feedbackId = feedbackId;
        }

        public Integer getRating() {
            return rating;
        }

        public void setRating(Integer rating) {
            this.rating = rating;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }

        public LocalDateTime getFeedbackTime() {
            return feedbackTime;
        }

        public void setFeedbackTime(LocalDateTime feedbackTime) {
            this.feedbackTime = feedbackTime;
        }
    }

    // 内部类：催单信息
    public static class UrgentRequestInfo {
        private Long urgentId;
        private String reason;
        private LocalDateTime urgentTime;
        private String status;
        private String statusDisplayName;

        public UrgentRequestInfo() {}

        public UrgentRequestInfo(Long urgentId, String reason, LocalDateTime urgentTime,
                               String status, String statusDisplayName) {
            this.urgentId = urgentId;
            this.reason = reason;
            this.urgentTime = urgentTime;
            this.status = status;
            this.statusDisplayName = statusDisplayName;
        }

        public Long getUrgentId() {
            return urgentId;
        }

        public void setUrgentId(Long urgentId) {
            this.urgentId = urgentId;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public LocalDateTime getUrgentTime() {
            return urgentTime;
        }

        public void setUrgentTime(LocalDateTime urgentTime) {
            this.urgentTime = urgentTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getStatusDisplayName() {
            return statusDisplayName;
        }

        public void setStatusDisplayName(String statusDisplayName) {
            this.statusDisplayName = statusDisplayName;
        }
    }
}
