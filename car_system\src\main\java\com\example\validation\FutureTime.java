package com.example.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 验证时间必须在未来的自定义注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FutureTimeValidator.class)
@Documented
public @interface FutureTime {
    
    String message() default "期望维修时间必须在当前时间之后";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
