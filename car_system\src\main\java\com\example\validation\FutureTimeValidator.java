package com.example.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDateTime;

/**
 * 验证时间必须在未来的验证器
 */
public class FutureTimeValidator implements ConstraintValidator<FutureTime, LocalDateTime> {

    @Override
    public void initialize(FutureTime constraintAnnotation) {
        // 初始化方法，可以在这里获取注解参数
    }

    @Override
    public boolean isValid(LocalDateTime value, ConstraintValidatorContext context) {
        // 如果值为null，让其他验证注解处理
        if (value == null) {
            return true;
        }

        LocalDateTime now = LocalDateTime.now();

        // 验证时间是否在当前时间之后
        if (!value.isAfter(now)) {
            // 自定义错误消息
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("期望维修时间必须是未来的时间")
                   .addConstraintViolation();
            return false;
        }

        // 验证是否在工作时间内（8:00-18:00）
        int hour = value.getHour();
        if (hour < 8 || hour >= 18) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("期望维修时间必须在工作时间内（8:00-18:00）")
                   .addConstraintViolation();
            return false;
        }

        return true;
    }
}
