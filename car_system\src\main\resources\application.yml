server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: vehicle-repair-management-system

  # Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        '[format_sql]': true

  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console

  # Jackson Configuration
  jackson:
    default-property-inclusion: non-null
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd'T'HH:mm:ss

# JWT Configuration
jwt:
  secret: VGhpcyBpcyBhIHNlY3JldCBrZXkgZm9yIEpXVCB0b2tlbiBnZW5lcmF0aW9uIGFuZCB2YWxpZGF0aW9u
  expiration: 86400 # 24 hours in seconds
  refresh-expiration: 604800 # 7 days in seconds

# Application Configuration
app:
  default-admin:
    username: admin
    password: admin123
    real-name: 系统管理员
    phone: 13800000000
    email: <EMAIL>

  specialties:
    - code: engine
      name: 机修
    - code: transmission
      name: 变速箱工
    - code: brake
      name: 制动工
    - code: electrical
      name: 电工
    - code: hvac
      name: 空调工
    - code: chassis
      name: 底盘工
    - code: body
      name: 漆工
    - code: tire
      name: 轮胎工

# Logging Configuration
logging:
  level:
    '[com.example]': DEBUG
    '[org.springframework.security]': DEBUG
    '[org.hibernate.SQL]': DEBUG
    '[org.hibernate.type.descriptor.sql.BasicBinder]': TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: WHEN_AUTHORIZED
