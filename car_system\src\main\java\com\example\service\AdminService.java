package com.example.service;

import com.example.dto.request.UserRegistrationRequest;
import com.example.dto.response.PageResponse;
import com.example.dto.response.UserDTO;
import com.example.dto.response.TechnicianDTO;
import com.example.dto.response.VehicleDTO;
import com.example.dto.response.OrderDTO;
import com.example.entity.User;
import com.example.entity.Technician;
import com.example.entity.Vehicle;
import com.example.entity.RepairOrder;
import com.example.entity.OrderFeedback;
import com.example.exception.BusinessException;
import com.example.exception.ResourceNotFoundException;
import com.example.repository.UserRepository;
import com.example.repository.TechnicianRepository;
import com.example.repository.VehicleRepository;
import com.example.repository.RepairOrderRepository;
import com.example.config.AppProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员服务
 */
@Service
@Transactional
public class AdminService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TechnicianRepository technicianRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AppProperties appProperties;

    /**
     * 创建管理员账户
     */
    public UserDTO createAdministrator(UserRegistrationRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查手机号是否已存在
        if (userRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建管理员用户
        User admin = new User();
        admin.setUsername(request.getUsername());
        admin.setPassword(passwordEncoder.encode(request.getPassword()));
        admin.setRealName(request.getRealName());
        admin.setPhone(request.getPhone());
        admin.setEmail(request.getEmail());
        admin.setAddress(request.getAddress());
        admin.setUserType(User.UserType.ADMIN);
        admin.setStatus(1);

        User savedAdmin = userRepository.save(admin);
        return convertUserToDTO(savedAdmin);
    }

    /**
     * 获取所有用户
     */
    @Transactional(readOnly = true)
    public PageResponse<UserDTO> getAllUsers(String search, Integer status, Pageable pageable) {
        Specification<User> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 只查询普通用户，不包括管理员
            predicates.add(criteriaBuilder.equal(root.get("userType"), User.UserType.USER));

            if (search != null && !search.trim().isEmpty()) {
                String searchPattern = "%" + search.trim().toLowerCase() + "%";
                Predicate usernamePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("username")), searchPattern);
                Predicate realNamePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("realName")), searchPattern);
                predicates.add(criteriaBuilder.or(usernamePredicate, realNamePredicate));
            }

            // 状态筛选：null表示显示全部，1表示有效用户，0表示无效用户
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<User> userPage = userRepository.findAll(spec, pageable);
        List<UserDTO> userDTOs = userPage.getContent().stream()
                .map(this::convertUserToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(userDTOs, new PageResponse.PageInfo(
                userPage.getNumber() + 1,
                userPage.getSize(),
                userPage.getTotalElements(),
                userPage.getTotalPages()
        ));
    }

    /**
     * 获取所有技师
     */
    @Transactional(readOnly = true)
    public PageResponse<TechnicianDTO> getAllTechnicians(String search, String specialty, Integer status,
                                                       LocalDate startDate, LocalDate endDate, Pageable pageable) {
        Specification<Technician> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 搜索条件（姓名、用户名、手机号）
            if (search != null && !search.trim().isEmpty()) {
                String searchPattern = "%" + search.trim().toLowerCase() + "%";
                Predicate searchPredicate = criteriaBuilder.or(
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("realName")), searchPattern),
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("username")), searchPattern),
                    criteriaBuilder.like(root.get("phone"), searchPattern)
                );
                predicates.add(searchPredicate);
            }

            // 工种筛选
            if (specialty != null && !specialty.trim().isEmpty()) {
                try {
                    Technician.Specialty specialtyEnum = Technician.Specialty.valueOf(specialty.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("specialty"), specialtyEnum));
                } catch (IllegalArgumentException e) {
                    // 无效的工种，返回空结果
                    predicates.add(criteriaBuilder.equal(root.get("technicianId"), -1L));
                }
            }

            // 状态筛选
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }

            // 入职时间范围筛选
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
                predicates.add(criteriaBuilder.between(root.get("hireDate"), startDateTime, endDateTime));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<Technician> technicianPage = technicianRepository.findAll(spec, pageable);
        List<TechnicianDTO> technicianDTOs = technicianPage.getContent().stream()
                .map(this::convertTechnicianToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(technicianDTOs, new PageResponse.PageInfo(
                technicianPage.getNumber() + 1,
                technicianPage.getSize(),
                technicianPage.getTotalElements(),
                technicianPage.getTotalPages()
        ));
    }

    /**
     * 获取所有车辆
     */
    @Transactional(readOnly = true)
    public PageResponse<VehicleDTO> getAllVehicles(String search, String brand, Pageable pageable) {
        Specification<Vehicle> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (search != null && !search.trim().isEmpty()) {
                String searchPattern = "%" + search.trim().toLowerCase() + "%";
                Predicate licensePlatePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("licensePlate")), searchPattern);
                Predicate brandPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("brand")), searchPattern);
                Predicate modelPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("model")), searchPattern);
                predicates.add(criteriaBuilder.or(licensePlatePredicate, brandPredicate, modelPredicate));
            }

            if (brand != null && !brand.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(
                        criteriaBuilder.lower(root.get("brand")), brand.trim().toLowerCase()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<Vehicle> vehiclePage = vehicleRepository.findAll(spec, pageable);
        List<VehicleDTO> vehicleDTOs = vehiclePage.getContent().stream()
                .map(this::convertVehicleToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(vehicleDTOs, new PageResponse.PageInfo(
                vehiclePage.getNumber() + 1,
                vehiclePage.getSize(),
                vehiclePage.getTotalElements(),
                vehiclePage.getTotalPages()
        ));
    }

    /**
     * 获取所有工单
     */
    @Transactional(readOnly = true)
    public PageResponse<OrderDTO> getAllOrders(Long orderId, String status, String urgencyLevel, Long faultTypeId,
                                             String specialty, LocalDate startDate, LocalDate endDate, Pageable pageable) {
        Specification<RepairOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 工单号筛选
            if (orderId != null) {
                predicates.add(criteriaBuilder.equal(root.get("orderId"), orderId));
            }

            // 状态筛选
            if (status != null && !status.trim().isEmpty()) {
                try {
                    RepairOrder.OrderStatus orderStatus = RepairOrder.OrderStatus.valueOf(status.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("status"), orderStatus));
                } catch (IllegalArgumentException e) {
                    // 无效的状态，返回空结果
                    predicates.add(criteriaBuilder.equal(root.get("orderId"), -1L));
                }
            }

            // 紧急程度筛选
            if (urgencyLevel != null && !urgencyLevel.trim().isEmpty()) {
                try {
                    RepairOrder.UrgencyLevel urgency = RepairOrder.UrgencyLevel.valueOf(urgencyLevel.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("urgencyLevel"), urgency));
                } catch (IllegalArgumentException e) {
                    // 无效的紧急程度，返回空结果
                    predicates.add(criteriaBuilder.equal(root.get("orderId"), -1L));
                }
            }

            // 故障类型ID筛选
            if (faultTypeId != null) {
                predicates.add(criteriaBuilder.equal(root.get("faultType").get("faultTypeId"), faultTypeId));
            }

            // 工种筛选
            if (specialty != null && !specialty.trim().isEmpty()) {
                try {
                    Technician.Specialty specialtyEnum = Technician.Specialty.valueOf(specialty.toUpperCase());
                    predicates.add(criteriaBuilder.isMember(specialtyEnum, root.get("faultType").get("requiredSpecialties")));
                } catch (IllegalArgumentException e) {
                    // 无效的工种，返回空结果
                    predicates.add(criteriaBuilder.equal(root.get("orderId"), -1L));
                }
            }

            // 时间范围筛选
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
                predicates.add(criteriaBuilder.between(root.get("submitTime"), startDateTime, endDateTime));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<RepairOrder> orderPage = repairOrderRepository.findAll(spec, pageable);
        List<OrderDTO> orderDTOs = orderPage.getContent().stream()
                .map(this::convertOrderToDTO)
                .collect(Collectors.toList());

        return PageResponse.success(orderDTOs, new PageResponse.PageInfo(
                orderPage.getNumber() + 1,
                orderPage.getSize(),
                orderPage.getTotalElements(),
                orderPage.getTotalPages()
        ));
    }

    /**
     * 禁用用户
     */
    public void disableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        if (user.getUserType() == User.UserType.ADMIN) {
            throw new BusinessException("不能禁用管理员用户");
        }

        user.setStatus(0); // 禁用
        userRepository.save(user);
    }

    /**
     * 删除用户（真正删除，包括关联信息）
     */
    @Transactional
    public void deleteUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        if (user.getUserType() == User.UserType.ADMIN) {
            throw new BusinessException("不能删除管理员用户");
        }

        // 检查是否有未完成的工单
        long pendingOrderCount = repairOrderRepository.countByUserUserIdAndStatusIn(
            userId,
            Arrays.asList(
                RepairOrder.OrderStatus.PENDING,
                RepairOrder.OrderStatus.ASSIGNED,
                RepairOrder.OrderStatus.IN_PROGRESS
            )
        );

        if (pendingOrderCount > 0) {
            throw new BusinessException("用户还有未完成的工单，无法删除");
        }

        // 删除用户的车辆信息
        vehicleRepository.deleteByUserUserId(userId);

        // 删除用户信息
        userRepository.delete(user);
    }

    /**
     * 启用用户
     */
    public void enableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        if (user.getUserType() == User.UserType.ADMIN) {
            throw new BusinessException("不能操作管理员用户");
        }

        user.setStatus(1); // 启用
        userRepository.save(user);
    }

    /**
     * 删除技师
     */
    public void deleteTechnician(Long technicianId) {
        Technician technician = technicianRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("技师不存在"));

        technician.setStatus(0); // 软删除
        technicianRepository.save(technician);
    }

    // 转换方法
    private UserDTO convertUserToDTO(User user) {
        UserDTO dto = new UserDTO(
                user.getUserId(),
                user.getUsername(),
                user.getRealName(),
                user.getPhone(),
                user.getEmail(),
                user.getAddress(),
                user.getStatus(),
                user.getUserType().name().toLowerCase(),
                user.getCreateTime(),
                user.getUpdateTime()
        );

        // 计算车辆数量
        Long vehicleCount = vehicleRepository.countByUserUserId(user.getUserId());
        dto.setVehicleCount(vehicleCount);

        // 计算工单数量
        Long orderCount = repairOrderRepository.countByUserUserId(user.getUserId());
        dto.setOrderCount(orderCount);

        return dto;
    }

    private TechnicianDTO convertTechnicianToDTO(Technician technician) {
        String specialtyName = appProperties.getSpecialties().stream()
                .filter(s -> s.getCode().equals(technician.getSpecialty().name().toLowerCase()))
                .findFirst()
                .map(AppProperties.Specialty::getName)
                .orElse(technician.getSpecialty().name());

        // 实时计算当前工作负载（未完成的工单数量）
        int currentWorkload = calculateCurrentWorkload(technician.getTechnicianId());

        return new TechnicianDTO(
                technician.getTechnicianId(),
                technician.getUsername(),
                technician.getRealName(),
                technician.getPhone(),
                technician.getEmail(),
                technician.getSpecialty().name().toLowerCase(),
                specialtyName,
                technician.getHourlyRate(),
                technician.getHireDate(),
                technician.getStatus(),
                currentWorkload,
                technician.getRating(),
                technician.getCreateTime(),
                technician.getUpdateTime()
        );
    }

    /**
     * 计算技师当前工作负载（未完成的工单数量）
     */
    private int calculateCurrentWorkload(Long technicianId) {
        List<RepairOrder.OrderStatus> activeStatuses = Arrays.asList(
            RepairOrder.OrderStatus.PENDING,
            RepairOrder.OrderStatus.ASSIGNED,
            RepairOrder.OrderStatus.IN_PROGRESS
        );

        return (int) repairOrderRepository.countByAssignedTechniciansContainingAndStatusIn(
            technicianId, activeStatuses
        );
    }

    private VehicleDTO convertVehicleToDTO(Vehicle vehicle) {
        VehicleDTO dto = new VehicleDTO(
                vehicle.getVehicleId(),
                vehicle.getUser().getUserId(),
                vehicle.getLicensePlate(),
                vehicle.getBrand(),
                vehicle.getModel(),
                vehicle.getYear(),
                vehicle.getVin(),
                vehicle.getColor(),
                vehicle.getEngineNumber(),
                vehicle.getRegisterDate(),
                vehicle.getCreateTime(),
                vehicle.getUpdateTime()
        );

        // 设置用户信息
        dto.setUser(new VehicleDTO.UserInfo(
                vehicle.getUser().getUserId(),
                vehicle.getUser().getRealName(),
                vehicle.getUser().getPhone(),
                vehicle.getUser().getEmail()
        ));

        return dto;
    }

    private OrderDTO convertOrderToDTO(RepairOrder order) {
        OrderDTO dto = new OrderDTO();

        // 基本信息
        dto.setOrderId(order.getOrderId());
        dto.setUserId(order.getUser().getUserId());
        dto.setVehicleId(order.getVehicle().getVehicleId());
        dto.setFaultTypeId(order.getFaultType().getFaultTypeId());
        dto.setDescription(order.getDescription());
        dto.setUrgencyLevel(order.getUrgencyLevel().name().toLowerCase());
        dto.setSubmitTime(order.getSubmitTime());
        dto.setPreferredTime(order.getPreferredTime());
        dto.setEstimatedCompletionTime(order.getEstimatedCompletionTime());
        dto.setActualCompletionTime(order.getActualCompletionTime());
        dto.setStatus(order.getStatus().name().toLowerCase());
        dto.setPaymentStatus(order.getPaymentStatus() != null ? order.getPaymentStatus().name().toLowerCase() : null);
        dto.setContactPhone(order.getContactPhone());
        dto.setTotalLaborCost(order.getTotalLaborCost());
        dto.setTotalMaterialCost(order.getTotalMaterialCost());
        dto.setTotalCost(order.getTotalCost());
        dto.setWorkResult(order.getWorkResult());
        dto.setWorkingHours(order.getWorkingHours());

        // 用户信息
        dto.setUser(new OrderDTO.UserInfo(
                order.getUser().getUserId(),
                order.getUser().getUsername(),
                order.getUser().getRealName(),
                order.getUser().getPhone()
        ));

        // 车辆信息
        dto.setVehicle(new OrderDTO.VehicleInfo(
                order.getVehicle().getVehicleId(),
                order.getVehicle().getLicensePlate(),
                order.getVehicle().getBrand(),
                order.getVehicle().getModel()
        ));

        // 故障类型信息
        List<String> specialtyStrings = order.getFaultType().getRequiredSpecialties().stream()
                .map(specialty -> specialty.name().toLowerCase())
                .collect(Collectors.toList());

        dto.setFaultType(new OrderDTO.FaultTypeInfo(
                order.getFaultType().getFaultTypeId(),
                order.getFaultType().getTypeName(),
                specialtyStrings,
                order.getFaultType().getEstimatedHours()
        ));

        // 分配的技师信息
        List<OrderDTO.TechnicianInfo> technicianInfos = order.getAssignedTechnicians().stream()
                .map(tech -> new OrderDTO.TechnicianInfo(
                        tech.getTechnicianId(),
                        tech.getRealName(),
                        tech.getSpecialty().name().toLowerCase(),
                        tech.getPhone()
                ))
                .collect(Collectors.toList());
        dto.setAssignedTechnicians(technicianInfos);

        // 材料使用信息（管理员可以查看所有材料使用情况）
        List<OrderDTO.MaterialUsageInfo> materialUsageInfos = order.getMaterialUsages().stream()
                .map(usage -> new OrderDTO.MaterialUsageInfo(
                        usage.getUsageId(),
                        usage.getMaterial().getMaterialId(),
                        usage.getMaterial().getMaterialName(),
                        usage.getMaterial().getSpecification(),
                        usage.getMaterial().getUnit(),
                        usage.getMaterial().getUnitPrice(),
                        usage.getQuantity(),
                        usage.getTotalPrice(),
                        usage.getUseTime()
                ))
                .collect(Collectors.toList());
        dto.setMaterialUsages(materialUsageInfos);

        // 反馈信息
        if (order.getFeedback() != null) {
            OrderFeedback feedback = order.getFeedback();
            dto.setFeedback(new OrderDTO.FeedbackInfo(
                    feedback.getFeedbackId(),
                    feedback.getRating(),
                    feedback.getComment(),
                    feedback.getFeedbackTime()
            ));
        }

        // 催单信息（管理员可以查看所有催单记录）
        if (order.getUrgentRequests() != null && !order.getUrgentRequests().isEmpty()) {
            List<OrderDTO.UrgentRequestInfo> urgentRequestInfos = order.getUrgentRequests().stream()
                    .map(urgentRequest -> new OrderDTO.UrgentRequestInfo(
                            urgentRequest.getUrgentId(),
                            urgentRequest.getReason(),
                            urgentRequest.getUrgentTime(),
                            urgentRequest.getStatus().name().toLowerCase(),
                            urgentRequest.getStatus().getDisplayName()
                    ))
                    .collect(Collectors.toList());
            dto.setUrgentRequests(urgentRequestInfos);
        }

        return dto;
    }
}
