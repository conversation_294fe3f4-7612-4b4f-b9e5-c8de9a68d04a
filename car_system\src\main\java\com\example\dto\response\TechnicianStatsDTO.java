package com.example.dto.response;

import java.math.BigDecimal;

/**
 * 技师统计数据DTO
 */
public class TechnicianStatsDTO {

    // 今日统计
    private Integer todayPendingTasks;      // 今日待处理任务
    private Integer todayInProgressTasks;   // 今日进行中任务
    private Integer todayCompletedTasks;    // 今日完成任务
    private BigDecimal todayIncome;         // 今日收入

    // 本月统计
    private Integer monthlyCompletedTasks;  // 本月完成任务
    private BigDecimal monthlyHours;        // 本月工时
    private BigDecimal monthlyIncome;       // 本月收入
    private BigDecimal monthlyAvgRating;    // 本月平均评分

    // 总体统计
    private Integer totalCompletedTasks;    // 总完成任务
    private BigDecimal totalHours;          // 总工时
    private BigDecimal totalIncome;         // 总收入
    private BigDecimal overallAvgRating;    // 总体平均评分

    public TechnicianStatsDTO() {}

    public TechnicianStatsDTO(Integer todayPendingTasks, Integer todayInProgressTasks, 
                             Integer todayCompletedTasks, BigDecimal todayIncome,
                             Integer monthlyCompletedTasks, BigDecimal monthlyHours, 
                             BigDecimal monthlyIncome, BigDecimal monthlyAvgRating,
                             Integer totalCompletedTasks, BigDecimal totalHours, 
                             BigDecimal totalIncome, BigDecimal overallAvgRating) {
        this.todayPendingTasks = todayPendingTasks;
        this.todayInProgressTasks = todayInProgressTasks;
        this.todayCompletedTasks = todayCompletedTasks;
        this.todayIncome = todayIncome;
        this.monthlyCompletedTasks = monthlyCompletedTasks;
        this.monthlyHours = monthlyHours;
        this.monthlyIncome = monthlyIncome;
        this.monthlyAvgRating = monthlyAvgRating;
        this.totalCompletedTasks = totalCompletedTasks;
        this.totalHours = totalHours;
        this.totalIncome = totalIncome;
        this.overallAvgRating = overallAvgRating;
    }

    // Getters and Setters
    public Integer getTodayPendingTasks() {
        return todayPendingTasks;
    }

    public void setTodayPendingTasks(Integer todayPendingTasks) {
        this.todayPendingTasks = todayPendingTasks;
    }

    public Integer getTodayInProgressTasks() {
        return todayInProgressTasks;
    }

    public void setTodayInProgressTasks(Integer todayInProgressTasks) {
        this.todayInProgressTasks = todayInProgressTasks;
    }

    public Integer getTodayCompletedTasks() {
        return todayCompletedTasks;
    }

    public void setTodayCompletedTasks(Integer todayCompletedTasks) {
        this.todayCompletedTasks = todayCompletedTasks;
    }

    public BigDecimal getTodayIncome() {
        return todayIncome;
    }

    public void setTodayIncome(BigDecimal todayIncome) {
        this.todayIncome = todayIncome;
    }

    public Integer getMonthlyCompletedTasks() {
        return monthlyCompletedTasks;
    }

    public void setMonthlyCompletedTasks(Integer monthlyCompletedTasks) {
        this.monthlyCompletedTasks = monthlyCompletedTasks;
    }

    public BigDecimal getMonthlyHours() {
        return monthlyHours;
    }

    public void setMonthlyHours(BigDecimal monthlyHours) {
        this.monthlyHours = monthlyHours;
    }

    public BigDecimal getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(BigDecimal monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public BigDecimal getMonthlyAvgRating() {
        return monthlyAvgRating;
    }

    public void setMonthlyAvgRating(BigDecimal monthlyAvgRating) {
        this.monthlyAvgRating = monthlyAvgRating;
    }

    public Integer getTotalCompletedTasks() {
        return totalCompletedTasks;
    }

    public void setTotalCompletedTasks(Integer totalCompletedTasks) {
        this.totalCompletedTasks = totalCompletedTasks;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public BigDecimal getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(BigDecimal totalIncome) {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getOverallAvgRating() {
        return overallAvgRating;
    }

    public void setOverallAvgRating(BigDecimal overallAvgRating) {
        this.overallAvgRating = overallAvgRating;
    }
}
