<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <h1>系统管理仪表盘</h1>
      <p>欢迎使用车辆维修管理系统，今天是 {{ currentDate }}</p>
    </div>

    <!-- 核心统计 -->
    <div class="stats-overview">
      <h2>系统概览</h2>
      <div v-loading="statsLoading" class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon size="24"><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">注册用户</div>
              <div class="stat-change positive">+{{ stats.newUsersToday }} 今日新增</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon technicians">
              <el-icon size="24"><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalTechnicians }}</div>
              <div class="stat-label">维修技师</div>
              <div class="stat-change positive">+{{ stats.newTechniciansToday }} 今日新增</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon vehicles">
              <el-icon size="24"><Car /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalVehicles }}</div>
              <div class="stat-label">注册车辆</div>
              <div class="stat-change positive">+{{ stats.newVehiclesToday }} 今日新增</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon orders">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalOrders }}</div>
              <div class="stat-label">维修工单</div>
              <div class="stat-change positive">+{{ stats.newOrdersToday }} 今日新增</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 实时数据 -->
    <div class="realtime-data">
      <div v-loading="statsLoading" class="data-row">
        <el-card class="data-card">
          <template #header>
            <span>今日工单状态</span>
          </template>
          <div class="order-status-grid">
            <div class="status-item">
              <div class="status-number pending">{{ todayOrders.pending }}</div>
              <div class="status-label">待处理</div>
            </div>
            <div class="status-item">
              <div class="status-number progress">{{ todayOrders.inProgress }}</div>
              <div class="status-label">进行中</div>
            </div>
            <div class="status-item">
              <div class="status-number completed">{{ todayOrders.completed }}</div>
              <div class="status-label">已完成</div>
            </div>
            <div class="status-item">
              <div class="status-number cancelled">{{ todayOrders.cancelled }}</div>
              <div class="status-label">已取消</div>
            </div>
          </div>
        </el-card>

        <el-card class="data-card">
          <template #header>
            <span>技师工作状态</span>
          </template>
          <div class="technician-status">
            <div class="status-row">
              <span class="status-label">在线技师</span>
              <span class="status-value online">{{ technicianStatus.online }}</span>
            </div>
            <div class="status-row">
              <span class="status-label">工作中</span>
              <span class="status-value working">{{ technicianStatus.working }}</span>
            </div>
            <div class="status-row">
              <span class="status-label">空闲中</span>
              <span class="status-value idle">{{ technicianStatus.idle }}</span>
            </div>
            <div class="status-row">
              <span class="status-label">离线</span>
              <span class="status-value offline">{{ technicianStatus.offline }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2>快速操作</h2>
      <div class="action-grid">
        <el-card class="action-card" @click="$router.push('/admin/users')">
          <div class="action-content">
            <el-icon size="32" color="#409eff"><User /></el-icon>
            <div class="action-title">用户管理</div>
            <div class="action-desc">管理系统用户账户</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin/technicians')">
          <div class="action-content">
            <el-icon size="32" color="#67c23a"><UserFilled /></el-icon>
            <div class="action-title">技师管理</div>
            <div class="action-desc">管理维修技师信息</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin/orders')">
          <div class="action-content">
            <el-icon size="32" color="#e6a23c"><Document /></el-icon>
            <div class="action-title">工单管理</div>
            <div class="action-desc">查看和管理维修工单</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin/analytics')">
          <div class="action-content">
            <el-icon size="32" color="#f56c6c"><TrendCharts /></el-icon>
            <div class="action-title">数据统计</div>
            <div class="action-desc">查看系统运营数据</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin/fault-types')">
          <div class="action-content">
            <el-icon size="32" color="#909399"><Setting /></el-icon>
            <div class="action-title">故障类型</div>
            <div class="action-desc">管理故障类型配置</div>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin/materials')">
          <div class="action-content">
            <el-icon size="32" color="#606266"><Box /></el-icon>
            <div class="action-title">材料管理</div>
            <div class="action-desc">管理维修材料库存</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <h2>最近活动</h2>
      <el-card>
        <el-table
          v-loading="activitiesLoading"
          :data="recentActivities"
          style="width: 100%"
          empty-text="暂无活动记录"
        >
          <el-table-column label="时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.time) }}
            </template>
          </el-table-column>

          <el-table-column label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getActivityType(row.type)" size="small">
                {{ getActivityText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" />

          <el-table-column prop="operator" label="操作人" width="120" />
        </el-table>


      </el-card>
    </div>

    <!-- 系统状态 -->
    <div class="system-status">
      <h2>系统状态</h2>
      <div class="status-grid">
        <el-card class="status-card">
          <template #header>
            <span>服务状态</span>
          </template>
          <div class="service-status">
            <div class="service-item">
              <div class="service-name">API 服务</div>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
            <div class="service-item">
              <div class="service-name">数据库</div>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
            <div class="service-item">
              <div class="service-name">缓存服务</div>
              <el-tag type="success" size="small">正常</el-tag>
            </div>
            <div class="service-item">
              <div class="service-name">文件存储</div>
              <el-tag type="warning" size="small">警告</el-tag>
            </div>
          </div>
        </el-card>

        <el-card class="status-card">
          <template #header>
            <span>性能指标</span>
          </template>
          <div class="performance-metrics">
            <div class="metric-item">
              <div class="metric-label">CPU 使用率</div>
              <el-progress :percentage="45" :stroke-width="8" />
            </div>
            <div class="metric-item">
              <div class="metric-label">内存使用率</div>
              <el-progress :percentage="67" :stroke-width="8" color="#e6a23c" />
            </div>
            <div class="metric-item">
              <div class="metric-label">磁盘使用率</div>
              <el-progress :percentage="23" :stroke-width="8" color="#67c23a" />
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, UserFilled, Van, Document, TrendCharts, Setting, Box
} from '@element-plus/icons-vue'

// 使用 Van 图标代替 Car
const Car = Van
import { adminAPI } from '@/api/admin'
import { analyticsAPI } from '@/api/analytics'
import dayjs from 'dayjs'

// 计算属性
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

// 响应式数据
const activitiesLoading = ref(false)
const statsLoading = ref(false)
const recentActivities = ref([])

// 统计数据
const stats = reactive({
  totalUsers: 0,
  totalTechnicians: 0,
  totalVehicles: 0,
  totalOrders: 0,
  newUsersToday: 0,
  newTechniciansToday: 0,
  newVehiclesToday: 0,
  newOrdersToday: 0
})

// 今日工单状态
const todayOrders = reactive({
  pending: 0,
  inProgress: 0,
  completed: 0,
  cancelled: 0
})

// 技师状态
const technicianStatus = reactive({
  online: 0,
  working: 0,
  idle: 0,
  offline: 0
})

// 获取统计数据
const fetchStats = async () => {
  try {
    statsLoading.value = true
    // 获取基础统计数据
    const [usersResponse, techniciansResponse, vehiclesResponse, ordersResponse] = await Promise.all([
      adminAPI.getAllUsers({ page: 1, size: 1 }),
      adminAPI.getAllTechnicians({ page: 1, size: 1 }),
      adminAPI.getAllVehicles({ page: 1, size: 1 }),
      adminAPI.getAllOrders({ page: 1, size: 1 })
    ])

    stats.totalUsers = usersResponse.data.data?.page?.totalElements || 0
    stats.totalTechnicians = techniciansResponse.data.data?.page?.totalElements || 0
    stats.totalVehicles = vehiclesResponse.data.data?.page?.totalElements || 0
    stats.totalOrders = ordersResponse.data.data?.page?.totalElements || 0

    // 获取今日数据统计
    const today = dayjs().format('YYYY-MM-DD')
    const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')

    // 获取今日新增用户
    try {
      const todayUsersResponse = await adminAPI.getAllUsers({
        page: 1,
        size: 1000,
        startDate: today,
        endDate: today
      })
      stats.newUsersToday = todayUsersResponse.data.data?.content?.length || 0
    } catch (error) {
      console.warn('Failed to fetch today users:', error)
      stats.newUsersToday = 0
    }

    // 获取今日新增技师
    try {
      const todayTechniciansResponse = await adminAPI.getAllTechnicians({
        page: 1,
        size: 1000,
        startDate: today,
        endDate: today
      })
      stats.newTechniciansToday = todayTechniciansResponse.data.data?.content?.length || 0
    } catch (error) {
      console.warn('Failed to fetch today technicians:', error)
      stats.newTechniciansToday = 0
    }

    // 获取今日新增车辆
    try {
      const todayVehiclesResponse = await adminAPI.getAllVehicles({
        page: 1,
        size: 1000,
        startDate: today,
        endDate: today
      })
      stats.newVehiclesToday = todayVehiclesResponse.data.data?.content?.length || 0
    } catch (error) {
      console.warn('Failed to fetch today vehicles:', error)
      stats.newVehiclesToday = 0
    }

    // 获取今日工单统计
    try {
      const todayOrdersResponse = await adminAPI.getAllOrders({
        page: 1,
        size: 1000,
        startDate: today,
        endDate: today
      })
      const todayOrdersList = todayOrdersResponse.data.data?.content || []
      stats.newOrdersToday = todayOrdersList.length

      // 统计今日工单状态
      todayOrders.pending = todayOrdersList.filter(o => o.status === 'pending').length
      todayOrders.inProgress = todayOrdersList.filter(o => o.status === 'in_progress' || o.status === 'assigned').length
      todayOrders.completed = todayOrdersList.filter(o => o.status === 'completed').length
      todayOrders.cancelled = todayOrdersList.filter(o => o.status === 'cancelled').length
    } catch (error) {
      console.warn('Failed to fetch today orders:', error)
      stats.newOrdersToday = 0
      todayOrders.pending = 0
      todayOrders.inProgress = 0
      todayOrders.completed = 0
      todayOrders.cancelled = 0
    }

    // 获取技师工作负载统计
    try {
      const workloadResponse = await analyticsAPI.getWorkloadStats({
        startDate: today,
        endDate: today
      })
      const workloadData = workloadResponse.data || []

      if (workloadData.length > 0) {
        technicianStatus.working = workloadData.filter(t => t.assignedCount > 0).length
        technicianStatus.idle = workloadData.filter(t => t.assignedCount === 0).length
        technicianStatus.online = technicianStatus.working + technicianStatus.idle
        technicianStatus.offline = Math.max(0, stats.totalTechnicians - technicianStatus.online)
      } else {
        // 如果没有工作负载数据，使用估算
        technicianStatus.online = Math.floor(stats.totalTechnicians * 0.7)
        technicianStatus.working = Math.floor(stats.totalTechnicians * 0.4)
        technicianStatus.idle = technicianStatus.online - technicianStatus.working
        technicianStatus.offline = stats.totalTechnicians - technicianStatus.online
      }
    } catch (error) {
      console.warn('Failed to fetch workload stats:', error)
      // 使用估算数据
      technicianStatus.online = Math.floor(stats.totalTechnicians * 0.7)
      technicianStatus.working = Math.floor(stats.totalTechnicians * 0.4)
      technicianStatus.idle = technicianStatus.online - technicianStatus.working
      technicianStatus.offline = stats.totalTechnicians - technicianStatus.online
    }

  } catch (error) {
    console.error('Failed to fetch stats:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 获取最近活动
const fetchRecentActivities = async () => {
  try {
    activitiesLoading.value = true
    const activities = []

    // 获取最近的工单活动
    try {
      const recentOrdersResponse = await adminAPI.getAllOrders({
        page: 1,
        size: 10,
        sortBy: 'updateTime',
        sortOrder: 'desc'
      })
      const recentOrders = recentOrdersResponse.data.data?.content || []

      recentOrders.slice(0, 3).forEach(order => {
        // 获取操作员名称：优先显示技师，其次显示用户
        let operator = '系统'
        if (order.assignedTechnicians && order.assignedTechnicians.length > 0) {
          operator = order.assignedTechnicians[0].realName
        } else if (order.user && order.user.realName) {
          operator = order.user.realName
        }

        activities.push({
          time: order.updateTime || order.createTime,
          type: 'order',
          description: `工单 #${order.orderId} ${getOrderStatusText(order.status)}`,
          operator: operator
        })
      })
    } catch (error) {
      console.warn('Failed to fetch recent orders:', error)
    }

    // 获取最近的用户注册
    try {
      const recentUsersResponse = await adminAPI.getAllUsers({
        page: 1,
        size: 5,
        sortBy: 'createTime',
        sortOrder: 'desc'
      })
      const recentUsers = recentUsersResponse.data.data?.content || []

      recentUsers.slice(0, 2).forEach(user => {
        activities.push({
          time: user.createTime,
          type: 'user',
          description: `新用户 ${user.realName || user.username} 完成注册`,
          operator: '系统'
        })
      })
    } catch (error) {
      console.warn('Failed to fetch recent users:', error)
    }

    // 如果没有获取到足够的活动数据，添加一些系统活动
    if (activities.length < 3) {
      activities.push({
        time: dayjs().subtract(30, 'minute').toISOString(),
        type: 'system',
        description: '系统进行了定期数据备份',
        operator: '系统'
      })
    }

    // 按时间排序
    activities.sort((a, b) => dayjs(b.time).valueOf() - dayjs(a.time).valueOf())

    recentActivities.value = activities.slice(0, 5)
  } catch (error) {
    console.error('Failed to fetch recent activities:', error)
    // 如果获取失败，提供一些基本的系统活动
    recentActivities.value = [
      {
        time: dayjs().subtract(10, 'minute').toISOString(),
        type: 'system',
        description: '系统运行正常',
        operator: '系统'
      }
    ]
  } finally {
    activitiesLoading.value = false
  }
}



// 获取工单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'assigned': '已分配',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取活动类型
const getActivityType = (type) => {
  const typeMap = {
    order: 'primary',
    user: 'success',
    technician: 'warning',
    system: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取活动文本
const getActivityText = (type) => {
  const textMap = {
    order: '工单',
    user: '用户',
    technician: '技师',
    system: '系统'
  }
  return textMap[type] || type
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats()
  fetchRecentActivities()
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 30px;
}

.stats-overview h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  cursor: default;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.stat-icon.users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.technicians {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.vehicles {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.stat-change {
  font-size: 12px;
}

.stat-change.positive {
  color: #67c23a;
}

/* 实时数据 */
.realtime-data {
  margin-bottom: 30px;
}

.data-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.order-status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.status-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-number {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.status-number.pending {
  color: #e6a23c;
}

.status-number.progress {
  color: #409eff;
}

.status-number.completed {
  color: #67c23a;
}

.status-number.cancelled {
  color: #f56c6c;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.technician-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.status-row:last-child {
  border-bottom: none;
}

.status-value {
  font-weight: 600;
  font-size: 16px;
}

.status-value.online {
  color: #67c23a;
}

.status-value.working {
  color: #409eff;
}

.status-value.idle {
  color: #e6a23c;
}

.status-value.offline {
  color: #909399;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 20px;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 15px 0 5px 0;
}

.action-desc {
  font-size: 14px;
  color: #666;
}

/* 最近活动 */
.recent-activities {
  margin-bottom: 30px;
}

.recent-activities h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}



/* 系统状态 */
.system-status h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.service-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.service-item:last-child {
  border-bottom: none;
}

.service-name {
  font-weight: 500;
  color: #333;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .dashboard-header h1 {
    font-size: 24px;
  }

  .order-status-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .data-row {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
