<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>请选择注册类型并填写信息</p>
      </div>

      <el-tabs v-model="activeTab" class="register-tabs">
        <!-- 普通用户注册 -->
        <el-tab-pane label="普通用户" name="user">
          <el-form
            ref="userFormRef"
            :model="userForm"
            :rules="userRules"
            class="register-form"
            label-width="80px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="userForm.username" placeholder="请输入用户名" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input
                v-model="userForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="userForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>

            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="userForm.phone" placeholder="请输入手机号码" />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>

            <el-form-item label="地址" prop="address">
              <el-input
                v-model="userForm.address"
                type="textarea"
                placeholder="请输入地址"
                :rows="2"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                @click="handleUserRegister"
              >
                注册
              </el-button>
              <el-button @click="resetUserForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 技师注册 -->
        <el-tab-pane label="维修技师" name="technician">
          <el-form
            ref="technicianFormRef"
            :model="technicianForm"
            :rules="technicianRules"
            class="register-form"
            label-width="80px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="technicianForm.username" placeholder="请输入用户名" />
            </el-form-item>

            <el-form-item label="密码" prop="password">
              <el-input
                v-model="technicianForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="technicianForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="technicianForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>

            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="technicianForm.phone" placeholder="请输入手机号码" />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="technicianForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>

            <el-form-item label="专业工种" prop="specialty">
              <el-select
                v-model="technicianForm.specialty"
                placeholder="请选择专业工种"
                style="width: 100%"
                :loading="specialtiesLoading"
                filterable
              >
                <el-option
                  v-for="specialty in specialties"
                  :key="specialty.code"
                  :label="`${specialty.name} - ${specialty.description || ''}`"
                  :value="specialty.code"
                >
                  <div class="specialty-option">
                    <div class="specialty-name">{{ specialty.name }}</div>
                    <div class="specialty-desc">{{ specialty.description }}</div>
                  </div>
                </el-option>
              </el-select>
              <div class="specialty-hint">
                <el-text size="small" type="info">
                  注意：工种选择后不可修改，请谨慎选择
                </el-text>
              </div>
            </el-form-item>

            <el-form-item label="时薪" prop="hourlyRate">
              <el-input-number
                v-model="technicianForm.hourlyRate"
                :min="0"
                :precision="2"
                placeholder="请输入时薪"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="入职日期" prop="hireDate">
              <el-date-picker
                v-model="technicianForm.hireDate"
                type="date"
                placeholder="请选择入职日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                @click="handleTechnicianRegister"
              >
                注册
              </el-button>
              <el-button @click="resetTechnicianForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="register-footer">
        <p>
          已有账户？
          <router-link to="/login" class="login-link">立即登录</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userAPI } from '@/api/user'
import { technicianAPI } from '@/api/technician'

const router = useRouter()

// 当前激活的标签页
const activeTab = ref('user')

// 表单引用
const userFormRef = ref()
const technicianFormRef = ref()

// 加载状态
const loading = ref(false)
const specialtiesLoading = ref(false)

// 工种列表
const specialties = ref([])

// 用户注册表单
const userForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phone: '',
  email: '',
  address: ''
})

// 技师注册表单
const technicianForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phone: '',
  email: '',
  specialty: '',
  hourlyRate: null,
  hireDate: ''
})

// 密码确认验证器
const validateConfirmPassword = (_rule, value, callback, form) => {
  if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 用户表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: (rule, value, callback) => validateConfirmPassword(rule, value, callback, userForm), trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' }
  ]
}

// 技师表单验证规则
const technicianRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: (rule, value, callback) => validateConfirmPassword(rule, value, callback, technicianForm), trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  specialty: [
    { required: true, message: '请选择专业工种', trigger: 'change' }
  ],
  hourlyRate: [
    { required: true, message: '请输入时薪', trigger: 'blur' },
    { type: 'number', min: 0, message: '时薪不能小于0', trigger: 'blur' }
  ],
  hireDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ]
}

// 工种映射
const specialtyMap = {
  engine: { name: '机修', description: '负责发动机相关故障的诊断和维修' },
  transmission: { name: '变速箱工', description: '负责变速箱系统的维修和保养' },
  brake: { name: '制动工', description: '负责刹车系统的检修和更换' },
  electrical: { name: '电工', description: '负责车辆电路和电子设备维修' },
  hvac: { name: '空调工', description: '负责空调制冷制热系统维修' },
  chassis: { name: '底盘工', description: '负责底盘悬挂转向系统维修' },
  body: { name: '漆工', description: '负责车身钣金喷漆等外观维修' },
  tire: { name: '轮胎工', description: '负责轮胎更换平衡四轮定位等' }
}

// 获取工种列表
const fetchSpecialties = async () => {
  try {
    specialtiesLoading.value = true
    const response = await technicianAPI.getSpecialties()
    const specialtyList = response.data || []

    // 安全地转换为选项格式，处理可能的对象或字符串数据
    specialties.value = specialtyList.map(item => {
      // 如果 item 是字符串，直接使用
      if (typeof item === 'string') {
        return {
          code: item,
          name: specialtyMap[item]?.name || item,
          description: specialtyMap[item]?.description || ''
        }
      }
      // 如果 item 是对象，提取 code 属性
      else if (typeof item === 'object' && item !== null) {
        const code = item.code || item
        return {
          code: code,
          name: specialtyMap[code]?.name || code,
          description: specialtyMap[code]?.description || ''
        }
      }
      // 其他情况，使用默认值
      else {
        return {
          code: item,
          name: String(item),
          description: ''
        }
      }
    })
  } catch (error) {
    console.error('Failed to fetch specialties:', error)
    // 使用默认工种列表
    specialties.value = Object.keys(specialtyMap).map(code => ({
      code,
      name: specialtyMap[code].name,
      description: specialtyMap[code].description
    }))
  } finally {
    specialtiesLoading.value = false
  }
}

// 处理用户注册
const handleUserRegister = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    loading.value = true

    const { confirmPassword, ...registerData } = userForm
    await userAPI.register(registerData)

    ElMessage.success('注册成功，请登录')
    router.push('/login')
  } catch (error) {
    console.error('User registration error:', error)
  } finally {
    loading.value = false
  }
}

// 处理技师注册
const handleTechnicianRegister = async () => {
  if (!technicianFormRef.value) return

  try {
    await technicianFormRef.value.validate()
    loading.value = true

    const { confirmPassword, ...registerData } = technicianForm
    await technicianAPI.register(registerData)

    ElMessage.success('注册成功，请登录')
    router.push('/login')
  } catch (error) {
    console.error('Technician registration error:', error)
  } finally {
    loading.value = false
  }
}

// 重置用户表单
const resetUserForm = () => {
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
}

// 重置技师表单
const resetTechnicianForm = () => {
  if (technicianFormRef.value) {
    technicianFormRef.value.resetFields()
  }
}

// 组件挂载时获取工种列表
onMounted(() => {
  fetchSpecialties()
})
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.register-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.register-tabs {
  margin-bottom: 20px;
}

.register-form {
  margin-top: 20px;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.register-footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-link {
  color: #409eff;
  text-decoration: none;
}

.login-link:hover {
  text-decoration: underline;
}

/* 工种选择样式 */
.specialty-option {
  padding: 5px 0;
}

.specialty-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.specialty-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.specialty-hint {
  margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-card {
    padding: 30px 20px;
  }

  .register-header h1 {
    font-size: 20px;
  }

  .register-form {
    :deep(.el-form-item__label) {
      width: 70px !important;
    }
  }
}

@media (max-width: 480px) {
  .register-form {
    :deep(.el-form-item) {
      flex-direction: column;
    }

    :deep(.el-form-item__label) {
      width: 100% !important;
      text-align: left;
      margin-bottom: 5px;
    }
  }
}
</style>
