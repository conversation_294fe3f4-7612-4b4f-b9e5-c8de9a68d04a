<template>
  <div class="technician-history">
    <div class="page-header">
      <h1>工作历史</h1>
      <p>查看您的历史维修记录和工作统计</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchHistory"
          />
        </el-form-item>

        <el-form-item label="工单状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            style="width: 150px"
            @change="fetchHistory"
          >
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="故障类型">
          <el-select
            v-model="filters.faultTypeId"
            placeholder="全部类型"
            clearable
            style="width: 150px"
            @change="fetchHistory"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchHistory">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.totalCompleted }}</div>
            <div class="stat-label">已完成工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon hours">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.totalHours }}h</div>
            <div class="stat-label">总工作时长</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon earnings">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">¥{{ stats.totalEarnings }}</div>
            <div class="stat-label">总收入</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon rating">
            <el-icon size="24"><Star /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.avgRating }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 历史记录列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-history-list">
        <div v-for="record in historyRecords" :key="record.orderId" class="mobile-history-card">
          <el-card shadow="hover" @click="viewOrderDetail(record)">
            <div class="history-info">
              <div class="history-header">
                <div class="order-id">工单 #{{ record.orderId }}</div>
                <el-tag :type="getStatusType(record.status)" size="small">
                  {{ getStatusText(record.status) }}
                </el-tag>
              </div>
              <div class="history-details">
                <div class="detail-item">
                  <span class="label">车辆:</span>
                  <span class="value">{{ record.vehicle?.licensePlate }} {{ record.vehicle?.brand }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">故障类型:</span>
                  <span class="value">{{ record.faultType?.typeName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">工作时长:</span>
                  <span class="value">{{ record.workingHours || 0 }}小时</span>
                </div>
                <div class="detail-item">
                  <span class="label">工时费:</span>
                  <span class="value">¥{{ calculateLaborCost(record) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">完成时间:</span>
                  <span class="value">{{ formatDate(record.actualCompletionTime) }}</span>
                </div>
                <div v-if="record.feedback && record.feedback.rating" class="detail-item">
                  <span class="label">用户评分:</span>
                  <span class="value">
                    <el-rate
                      v-model="record.feedback.rating"
                      disabled
                      size="small"
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                    />
                  </span>
                </div>
              </div>
              <div class="history-actions">
                <el-button type="primary" size="small" @click.stop="viewOrderDetail(record)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="historyRecords"
          style="width: 100%"
          empty-text="暂无历史记录"
          stripe
          highlight-current-row
          @row-click="viewOrderDetail"
        >
          <el-table-column prop="orderId" label="工单号" width="100" />

          <el-table-column label="车辆信息" width="180">
            <template #default="{ row }">
              <div class="vehicle-info">
                <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
                <div class="vehicle-detail">
                  {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

          <el-table-column label="故障描述" min-width="200">
            <template #default="{ row }">
              <el-tooltip
                :content="row.description"
                placement="top"
                :disabled="row.description.length <= 50"
              >
                <div class="description-text">
                  {{ row.description.length > 50 ? row.description.substring(0, 50) + '...' : row.description }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column label="工作时长" width="100" align="center">
            <template #default="{ row }">
              {{ row.workingHours || 0 }}h
            </template>
          </el-table-column>

          <el-table-column label="工时费" width="100" align="center">
            <template #default="{ row }">
              ¥{{ calculateLaborCost(row) }}
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="完成时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.actualCompletionTime) }}
            </template>
          </el-table-column>

          <el-table-column label="用户评分" width="120" align="center">
            <template #default="{ row }">
              <el-rate
                v-if="row.feedback && row.feedback.rating"
                v-model="row.feedback.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
              <span v-else class="no-rating">未评分</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click.stop="viewOrderDetail(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchHistory"
          @current-change="fetchHistory"
        />
      </div>
    </el-card>

    <!-- 工单详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="工单详情"
      width="900px"
      class="order-detail-dialog"
    >
      <div v-if="selectedOrder" class="order-detail">
        <!-- 基本信息 -->
        <el-card class="info-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span>基本信息</span>
              <el-tag :type="getStatusType(selectedOrder.status)" size="large">
                {{ getStatusText(selectedOrder.status) }}
              </el-tag>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="工单号">
              #{{ selectedOrder.orderId }}
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">
              {{ formatDate(selectedOrder.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="期望维修时间">
              {{ formatDate(selectedOrder.preferredTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="预估完成时间">
              {{ formatDate(selectedOrder.estimatedCompletionTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="实际完成时间">
              {{ formatDate(selectedOrder.actualCompletionTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ selectedOrder.contactPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 车辆和用户信息 -->
        <el-card class="vehicle-section" shadow="never">
          <template #header>
            <span>车辆信息</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="车牌号">
              {{ selectedOrder.vehicle?.licensePlate }}
            </el-descriptions-item>
            <el-descriptions-item label="车主">
              {{ selectedOrder.user?.realName }}
            </el-descriptions-item>
            <el-descriptions-item label="品牌型号">
              {{ selectedOrder.vehicle?.brand }} {{ selectedOrder.vehicle?.model }}
            </el-descriptions-item>
            <el-descriptions-item label="联系方式">
              {{ selectedOrder.user?.phone }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 故障信息 -->
        <el-card class="fault-section" shadow="never">
          <template #header>
            <span>故障信息</span>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="故障类型">
              {{ selectedOrder.faultType?.typeName }}
            </el-descriptions-item>
            <el-descriptions-item label="故障描述">
              {{ selectedOrder.description }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 技师团队信息 -->
        <el-card v-if="selectedOrder.assignedTechnicians?.length" class="team-section" shadow="never">
          <template #header>
            <span>技师团队</span>
          </template>

          <div class="technician-list">
            <div
              v-for="technician in selectedOrder.assignedTechnicians"
              :key="technician.technicianId"
              class="technician-item"
            >
              <el-avatar :size="40">{{ technician.realName?.charAt(0) || 'T' }}</el-avatar>
              <div class="technician-info">
                <div class="technician-name">{{ technician.realName }}</div>
                <div class="technician-specialty">{{ technician.specialty }}</div>
                <div class="technician-contact">{{ technician.phone }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 材料使用记录 -->
        <MaterialUsageCard
          v-if="selectedOrder.materialUsages && selectedOrder.materialUsages.length > 0"
          :material-usages="selectedOrder.materialUsages"
          class="material-section"
        />

        <!-- 费用信息 -->
        <el-card class="cost-section" shadow="never">
          <template #header>
            <span>费用信息</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="人工费">
              ¥{{ (parseFloat(calculateLaborCost(selectedOrder)) || 0).toFixed(2) }}
            </el-descriptions-item>
            <el-descriptions-item label="材料费">
              ¥{{ (parseFloat(selectedOrder.totalMaterialCost) || 0).toFixed(2) }}
            </el-descriptions-item>
            <el-descriptions-item label="总费用" :span="2">
              <span class="total-cost">¥{{ (parseFloat(selectedOrder.totalCost) || 0).toFixed(2) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 工作记录 -->
        <el-card v-if="selectedOrder.workResult || selectedOrder.workingHours" class="work-section" shadow="never">
          <template #header>
            <span>工作记录</span>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item v-if="selectedOrder.workingHours" label="实际工时">
              {{ selectedOrder.workingHours }}小时
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedOrder.workResult" label="维修结果">
              {{ selectedOrder.workResult }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 催单信息 -->
        <el-card v-if="selectedOrder.urgentRequests && selectedOrder.urgentRequests.length > 0" class="urgent-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span>催单记录</span>
              <el-tag type="warning" size="small">
                {{ selectedOrder.urgentRequests.length }}次
              </el-tag>
            </div>
          </template>

          <div class="urgent-list">
            <div
              v-for="urgent in selectedOrder.urgentRequests"
              :key="urgent.urgentId"
              class="urgent-item"
            >
              <div class="urgent-content">
                <div class="urgent-reason">{{ urgent.reason }}</div>
                <div class="urgent-time">催单时间: {{ formatDate(urgent.urgentTime) }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 用户反馈 -->
        <el-card v-if="selectedOrder.feedback" class="feedback-section" shadow="never">
          <template #header>
            <span>用户反馈</span>
          </template>

          <div class="feedback-content">
            <div class="feedback-rating">
              <span>评分: </span>
              <el-rate
                v-model="selectedOrder.feedback.rating"
                disabled
                show-score
                text-color="#ff9900"
              />
            </div>
            <div v-if="selectedOrder.feedback.comment" class="feedback-text">
              <p>{{ selectedOrder.feedback.comment }}</p>
            </div>
            <div class="feedback-time">
              反馈时间: {{ formatDate(selectedOrder.feedback.feedbackTime) }}
            </div>
          </div>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Refresh, Check, Clock, Money, Star, View
} from '@element-plus/icons-vue'
import { technicianAPI } from '@/api/technician'
import { faultTypeAPI } from '@/api/faultType'
import { useAppStore } from '@/stores/app'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import MaterialUsageCard from '@/components/MaterialUsageCard.vue'

const appStore = useAppStore()
const router = useRouter()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const historyRecords = ref([])
const faultTypes = ref([])
const selectedOrder = ref(null)
const currentTechnician = ref(null)

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  dateRange: null,
  status: '',
  faultTypeId: null
})

// 统计数据
const stats = reactive({
  totalCompleted: 0,
  totalHours: 0,
  totalEarnings: 0,
  avgRating: 0
})

// 详情对话框
const detailDialog = reactive({
  visible: false
})

// 获取历史记录
const fetchHistory = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加筛选条件
    if (filters.status) params.status = filters.status
    if (filters.faultTypeId) params.faultTypeId = filters.faultTypeId
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await technicianAPI.getCurrentTechnicianHistory(params)
    console.log('Technician History API Response:', response)
    console.log('Response data structure:', response.data)
    console.log('History content:', response.data.data?.content)
    console.log('Page info:', response.data.data?.page)

    // 处理分页响应数据结构
    if (response.data && response.data.data) {
      // PageResponse结构: { success: true, data: { content: [], page: {} } }
      historyRecords.value = response.data.data.content || []
      pagination.total = response.data.data.page?.totalElements || 0
    } else if (response.data && Array.isArray(response.data)) {
      // 直接返回数组的情况（向后兼容）
      historyRecords.value = response.data
      pagination.total = response.data.length
    } else {
      historyRecords.value = []
      pagination.total = 0
    }

    console.log('Final history records array:', historyRecords.value)
    console.log('Final pagination total:', pagination.total)

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('Failed to fetch history:', error)
    ElMessage.error('获取工作历史失败')
  } finally {
    loading.value = false
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    const response = await faultTypeAPI.getList()
    faultTypes.value = response.data.data?.content || response.data || []
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
  }
}

// 获取当前技师信息
const fetchCurrentTechnician = async () => {
  try {
    const response = await technicianAPI.getCurrentTechnician()
    currentTechnician.value = response.data.data || response.data || {}
  } catch (error) {
    console.error('Failed to fetch current technician:', error)
  }
}

// 计算工时费
const calculateLaborCost = (record) => {
  if (!record || !record.workingHours || !currentTechnician.value) {
    return 0
  }

  // 如果记录中已经有totalLaborCost，直接使用
  if (record.totalLaborCost) {
    return record.totalLaborCost
  }

  // 否则根据工时和时薪计算
  const workingHours = record.workingHours || 0
  const hourlyRate = currentTechnician.value.hourlyRate || 0
  return workingHours * hourlyRate
}

// 更新统计数据
const updateStats = () => {
  const completedRecords = historyRecords.value.filter(record => record.status === 'completed')

  stats.totalCompleted = completedRecords.length
  stats.totalHours = completedRecords.reduce((sum, record) => sum + (record.workingHours || 0), 0)
  stats.totalEarnings = completedRecords.reduce((sum, record) => sum + calculateLaborCost(record), 0)

  // 计算平均评分 - 从feedback对象中获取评分
  const ratedRecords = completedRecords.filter(record => record.feedback && record.feedback.rating)
  if (ratedRecords.length > 0) {
    const totalRating = ratedRecords.reduce((sum, record) => sum + record.feedback.rating, 0)
    stats.avgRating = (totalRating / ratedRecords.length).toFixed(1)
  } else {
    stats.avgRating = 0
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    dateRange: null,
    status: '',
    faultTypeId: null
  })
  pagination.page = 1
  fetchHistory()
}

// 查看工单详情
const viewOrderDetail = (order) => {
  selectedOrder.value = order
  detailDialog.visible = true
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    assigned: 'warning',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCurrentTechnician()
  fetchHistory()
  fetchFaultTypes()
})
</script>

<style scoped>
.technician-history {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.stat-icon.hours {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.earnings {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-icon.rating {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 移动端历史记录卡片 */
.mobile-history-list {
  display: grid;
  gap: 16px;
}

.mobile-history-card {
  cursor: pointer;
}

.history-info {
  padding: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-id {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.history-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  text-align: right;
  flex: 1;
}

.history-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 桌面端表格 */
.table-responsive {
  overflow-x: auto;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
}

.license-plate {
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-detail {
  font-size: 12px;
  color: #909399;
}

.description-text {
  line-height: 1.4;
}

.no-rating {
  color: #909399;
  font-style: italic;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 工单详情对话框 */
.order-detail-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.order-detail {
  display: grid;
  gap: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-section,
.vehicle-section,
.fault-section,
.team-section,
.material-section,
.cost-section,
.work-section,
.urgent-section,
.feedback-section {
  margin-bottom: 0;
}

/* 技师列表样式 */
.technician-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.technician-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.technician-info {
  margin-left: 15px;
  flex: 1;
}

.technician-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.technician-specialty {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.technician-contact {
  font-size: 12px;
  color: #999;
}

/* 费用信息样式 */
.total-cost {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
}

/* 催单信息样式 */
.urgent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.urgent-item {
  padding: 12px;
  background: #fff7e6;
  border-left: 4px solid #e6a23c;
  border-radius: 4px;
}

.urgent-reason {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.urgent-time {
  font-size: 12px;
  color: #999;
}

/* 反馈信息样式 */
.feedback-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.feedback-rating {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.feedback-rating span {
  margin-right: 10px;
  font-weight: 500;
}

.feedback-text {
  margin-bottom: 12px;
}

.feedback-text p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.feedback-time {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .technician-history {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .history-actions {
    justify-content: center;
  }

  .pagination-container {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .technician-history {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .history-actions .el-button {
    flex: 1;
    min-width: 0;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-item .value {
    text-align: left;
  }
}
</style>